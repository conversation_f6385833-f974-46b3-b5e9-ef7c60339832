### Compatibility

* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :warning:  2.0 | :no_entry: 1.9 | [...](Roadmap)

### Background

Obtain detailed network information from the PC such as `ip`, `mac`, and `hostname`.

### Caveats

* Since multiple adapters may be available on a PC, it may be required to [sift](data-filtering#network-information) through this information.
 * We attempt to locate the primary adapter automatically by making a socket connection to `google.com`.
 * If `google.com` is unreachable, you may [override this with a domain of your choosing](#override-default-domain).
 * Due to some edge-case configurations, the network information returned may be a virtual adapter (e.g. MAC address of zeros, etc).  For this reason, we're written a [data-filter](data-filtering#network-information) to help process the data using industry-wide MAC-address pattern matching.
* **2.0 Compatibility:** Some (but not all) of this information is backwards-compatible with 2.0 through a compatibility wrapper.


### Code

#### Primary Adapter

Using [`qz.networking.device()`](https://qz.io/api/qz.networking#.device), you can obtain **information about the primary adapter** used by the a PC.

```js
// Get information about a single device
qz.networking.device().then(device => console.log(device));

// Returns
▶️ {
   hostname: "inspiron",
   id: "Local Area Connection",
   ip: "***********",
   ip4: (1) ["***********"],
   ip6: (1) ["2001:0db8:85a3:0000:0000:8a2e:0370:7334"],
   mac: "C4910CB0B9A7",
   name: "Local Area Connection",
   primary: true,
   up: true,
   username: "Administrator"
}
```

To obtain only the MAC address of the PC:
```js
// Get information about a single network device
qz.networking.device().then(device => console.log(device.mac));

// Returns
"C4910CB0B9A7"
```

#### All Adapters

Using [`qz.networking.devices()`](https://qz.io/api/qz.networking#.devices), you can obtain an **array of all adapter information** used by the PC.

```js
qz.networking.devices().then(devices => console.log(devices));

// Returns
(3) [...]
▶️ 0: {name: "Local Area Connection", ip: "***********", ip4: Array(1), ip6: Array(1), primary: true, up: true, …}
▶️ 1: {name: "Wireless Network Connection", ip: "************", ip6: Array(1), primary: false, up: true, …}
▶️ 2: {name: "Loopback Adapter", ip: "127.0.0.1", ip4: Array(1), ip6: Array(1), primary: false, …}
```

... trouble locating the correct adapter?  Consider using a [data-filter](data-filtering#network-information).

### Hostname

Since 2.2.2: To get the computer's hostname only (this has an added benefit that it will never throw an exception when the [default domain](#override-default-domain) is unreachable)

```js
qz.networking.hostname().then(hostname => console.log(hostname));
```


#### Override Default Domain

By default, QZ Tray makes a socket connection to `google.com` to resolve the default network adapter.  If `google.com` is unreachable, you may use an IP or hostname of your choosing.

```js
// use 'yahoo.com' to identify the default network adapter
qz.networking.device('yahoo.com', 443).then(device => console.log(device));
```