### Compatibility
* ⛔️ 2.2 | :white_check_mark: 2.1 |:white_check_mark: 2.0 | ⛔️ 1.9 | [...](Roadmap)

### Objective
Learn the recommended way to install, remove, or upgrade Java

### Background

Oracle [drastically changed their licensing model](https://blogs.oracle.com/java-platform-group/extension-of-oracle-java-se-8-public-updates-and-java-web-start-support) and has stopped providing public updates for Java. The last free Oracle Java update was January 2019.

Because of this change, we have switched all development and testing to [Eclipse Adoptium](https://adoptium.net/).

* OpenJDK 11 64-bit with the Hotspot JVM is our recommended version of Java for all platforms.

> ⚠️ **Note:** QZ Tray 2.2 bundles Java, so these steps are irrelevant.

### Requirements

* QZ Tray 2.0.10 or newer

### Steps

1. [Windows](#windows)
1. [macOS](#macos)
1. [Linux](#linux)

### Windows

1. Make sure QZ Tray and other Java dependent software are closed
   * The easiest way to close QZ Tray is to go to the System Tray > QZ Tray > Exit
   * Check Windows Task Manager for other Java program. Look for `java.exe` or `javaw.exe` in the process name/details.

1. **Optional:** Uninstall all Java versions
   * If you do not require a specific version of Java for another program, it is highly recommended to uninstall all versions currently installed prior to updating
   * To uninstall, go to Control Panel > Programs  > Programs and Features

1. Download OpenJDK Temurin 11 64-bit (HotSpot JVM) [from adoptium.net](https://adoptium.net/releases.html?variant=openjdk11&jvmVariant=hotspot)

1. Run the installer (e.g. `OpenJDK11U-jdk_x64_windows_hotspot_11.0.14.1_1.msi`)
   * When asked to select features, take defaults

1. Verify the Java version
   * Open a `cmd` prompt and type `java -version`

      ![image](https://user-images.githubusercontent.com/12505463/153729803-e04fad1a-2244-4e56-afa0-8cbe544bdb7c.png)

1. Launch QZ Tray
   * If no shortcut has been created, `C:\ProgramData\Microsoft\Windows\Start Menu\Programs\QZ Tray`

### macOS

1. Make sure QZ Tray and other Java dependent software are closed
   * The easiest way to close QZ Tray is to go to the System Tray > QZ Tray > Exit
      * If QZ Tray is stuck in error you can close it and all Java processes using this command from terminal.<br>**Warning: This will end ALL Java apps that are running.**
      ```bash
      sudo pkill -9 java
      ```
1. **Optional:** Uninstall all Java versions
   * If you do not require a specific version of Java for another program, it is highly recommended to uninstall all versions currently installed prior to updating
   * Open a terminal (found in Applications > Utilities) and type the following commands:

     ```bash
     sudo rm -fr /Library/Internet\ Plug-Ins/JavaAppletPlugin.plugin \
     sudo rm -fr /Library/PreferencePanes/JavaControlPanel.prefpane \
     sudo rm -rf /Library/LaunchAgents/com.oracle.java.Java-Updater.plist \
     sudo rm -rf /Library/PrivilegedHelperTools/com.oracle.java.JavaUpdateHelper \
     sudo rm -rf /Library/LaunchDaemons/com.oracle.java.Helper-Tool.plist \
     sudo rm -rf /Library/Preferences/com.oracle.java.Helper-Tool.plist

     # WARNING: Don't type the <> symbols, change it for your jdk version!
     sudo rm -rf /Library/Java/JavaVirtualMachines/jdk<version>.jdk
     sudo rm -rf /Library/Java/JavaVirtualMachines/adopt<version>.jdk
     sudo rm -rf /Library/Java/JavaVirtualMachines/temurin<version>.jdk
     ```

1. Download OpenJDK 11 64-bit (HotSpot JVM) [from adoptium.net](https://adoptium.net/releases.html?variant=openjdk11&jvmVariant=hotspot)
 
   > **Note:** If the JDK version is installed, that must be upgraded as well, as QZ Tray defaults to the JDK version.

1. Run the installer and follow the prompts on the screen
   * The download will look similar to `OpenJDK11U-jdk_x64_mac_hotspot_11.0.3_7.pkg`
1. Verify the Java version
   * Open up a terminal and type `java -version`.  This will first return the JDK version on the system.  If JDK is not installed, it will return the JRE version. Whatever version is displayed from this command is the version of Java that QZ Tray will use.

   <img width="710" alt="image" src="https://user-images.githubusercontent.com/12505463/153729940-651217f1-1e92-4dd2-8c72-1d98c0d643e8.png">

1. Launch QZ Tray
 * If there is no desktop shortcut, start QZ Tray by going to Applications > QZ Tray

### Linux
Adoptium has a PPA for Debian, Rhel, and OpenSUSE flavors of Linux. Below are instructions for Ubuntu. Adoptium's documentation for other flavors [can be found here](https://github.com/adoptium/website-v2/blob/main/src/asciidoc-pages/installation/linux.adoc#linux-rpmdeb-installer-packages).

Alternatively, you may download and install OpenJDK11 [directly from adoptium.net](https://adoptium.net/releases.html?variant=openjdk11&jvmVariant=hotspot)

Note these instructions were adapted from [here](https://github.com/adoptium/website-v2/blob/main/src/asciidoc-pages/installation/linux.adoc#deb-installation-on-debian-or-ubuntu)
1. Make sure QZ Tray and other Java dependent software are closed
   * The easiest way to close QZ Tray is to go to the System Tray > QZ Tray > Exits

   ```bash
   # Ensure the necessary packages are present:
   sudo apt-get install -y wget apt-transport-https gnupg

   # Download the Eclipse Adoptium GPG key:
   wget https://packages.adoptium.net/artifactory/api/gpg/key/public && sudo apt-key add public

   # Configure the Eclipse Adoptium apt repository
   echo "deb https://packages.adoptium.net/artifactory/deb $(awk -F= '/^VERSION_CODENAME/{print$2}' /etc/os-release) main" | sudo tee /etc/apt/sources.list.d/adoptium.list

   # Install the Temurin version 11
   sudo apt-get update
   sudo apt-get install temurin-11-jdk
   ```

1. Verify Java Version

   ```bash
   java -version
   ```
   If the wrong version of Java is displayed, you can use this command to switch

   ```
   sudo update-alternatives --config java
   ```

   <img width="694" alt="image" src="https://user-images.githubusercontent.com/12505463/153765902-234c67be-2464-4fc0-b22a-006bc71695a2.png">

1. Launch QZ Tray

   If there is no desktop shortcut:
   * Search for "QZ Tray"
   * Go to `usr/share/applications` and find QZ Tray