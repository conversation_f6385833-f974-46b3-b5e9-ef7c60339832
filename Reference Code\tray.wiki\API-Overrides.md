### Compatibility

* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :white_check_mark: 2.0 | :no_entry: 1.9 | [...](Roadmap)

### Background

QZ Tray 2.1+ uses native promises and an internal sha256 hashing and requires only WebSocket dependency for connection and [`crypto` dependency for signing](signing).

<details>
<summary>Click to expand QZ Tray 2.0 overrides</summary>
<hr>

QZ Tray 2.0 is bundled with RSVP to provide [ECMAScript 6 Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) support. If RSVP is not desired, it can be overridden using [`qz.api.setPromiseType(...)`](https://qz.io/api/qz.api#.setPromiseType) to avoid `ReferenceError: RSVP is not defined` or `Uncaught TypeError: Cannot read property 'promise' of null`.  Override examples are provided below.


### Promises

1. Include the new promise library:

   ```html
   <script type="text/javascript" src="https://rawgit.com/kriskowal/q/v1/q.js"></script>
   ```
    > or via npm : `npm install q`


2. Override Promise with Q using [`qz.api.setPromiseType(...)`](https://qz.io/api/qz.api#.setPromiseType).

   ```javascript
   // Q
   qz.api.setPromiseType(require('q').Promise);
   // RSVP
   qz.api.setPromiseType(require('rsvp').Promise);
   // Bluebird
   qz.api.setPromiseType(resolver => new Promise(resolver));
   ```

<hr>
</details>


### WebSocket
As of Node 6.5, WebSockets are only available through 3rd party libraries causing `Error: WebSocket not supported by this browser`.

```js
qz.api.setWebSocketType(require('ws')); // require('websocket').w3cwebsocket
```

### Node Quickstart
Install dependencies:
```bash
npm install qz-tray ws
```

Provide API overrides and start talking to QZ Tray:
```js
var qz = require('qz-tray');
qz.api.setWebSocketType(require('ws'));

qz.websocket.connect()
.then(qz.printers.getDefault)
.then(function(printer) {
   console.log("The default printer is: " + printer);
})
.then(qz.websocket.disconnect)
.then(function() {
   process.exit(0);
})
.catch(function(err) {
   console.error(err);
   process.exit(1);
});
```

### AngularJS Quickstart
This is only for the API overrides.  To set up signing, see [`assets/signing/sign-message.ts`](https://github.com/qzind/tray/blob/master/assets/signing/sign-message.ts).

Install dependencies:
```bash
npm install qz-tray
```

Provide API overrides and start talking to QZ Tray:
```js
import * as qz from 'qz-tray';

qz.websocket.connect()
 .then(qz.printers.getDefault)
 .then(printer => console.log("The default printer is: " + printer))
 .then(qz.websocket.disconnect)
 .catch(err => console.error(err));
```
Note, Angular 9 and higher will error with the following:

```diff
- ERROR in ./node_modules/qz-tray/qz-tray.js
- Module not found: Error: Can't resolve 'path' in 'node_modules/qz-tray'
```

Add the following entry to your `package.json` to omit `path` from AOT compilation:

```js
"browser": { "path": false }
```
Note, Angular 11 and higher will error with the following:

```diff
- Could not find a declaration file for module 'qz-tray'. '/Users/<USER>/my-app/node_modules/qz-tray/qz-tray.js' implicitly has an 'any' type.
```

Add a file to `src/qz-tray.d.ts` with the following content:
```ts
declare module 'qz-tray';
```

<details>
<summary>QZ Tray 2.0 (continued)</summary>

### Native Promises
1. Override RSVP with native Promises using [`qz.api.setPromiseType(...)`](https://qz.io/api/qz.api#.setPromiseType).

 ```javascript
qz.api.setPromiseType(function promise(resolver) { return new Promise(resolver); });
 ```

### Override SHA256
A hashing algorithm is required for signature validation.  Use [`qz.api.setSha256Type(...)`](https://qz.io/api/qz.api#.setSha256Type) to override the default hashing library and avoid `TypeError: _qz.tools.hash is not a function`.

#### Native

Since QZ Tray 2.0.5, the [native browser `crypto` can be used](https://github.com/qzind/tray/pull/247#issuecomment-336617879).  This will [only work with HTTPS](https://bugs.chromium.org/p/chromium/issues/detail?id=373032) pages.

#### Node 6.5

```javascript
qz.api.setSha256Type(function(data) {
   return crypto.createHash('sha256').update(data).digest('hex');
});
```
 * `2.0.1` and older only.  Newer versions include this logic by default.

#### Node 4.5

 * Requires `sha.js`

```js
var createHash = require('sha.js');
qz.api.setSha256Type(function(data) {
    return createHash('sha256').update(data).digest('hex');
});
```

```bash
npm install js-sha256
```

```js
import { sha256 } from 'js-sha256';                       // QZ Tray 2.0 and older

qz.api.setSha256Type(data => sha256(data));               // QZ Tray 2.0 and older
qz.api.setPromiseType(resolver => new Promise(resolver)); // QZ Tray 2.0 and older
```

</details>