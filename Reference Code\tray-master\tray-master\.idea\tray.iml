<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager">
    <output url="file://$MODULE_DIR$/out/production" />
    <output-test url="file://$MODULE_DIR$/out/test" />
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/test" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/out" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="JQuery" level="application" />
    <orderEntry type="library" name="bootstrap" level="application" />
    <orderEntry type="library" name="bootstrap-select" level="application" />
    <orderEntry type="module-library">
      <library>
        <CLASSES>
          <root url="file://$MODULE_DIR$/lib/printing" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
        <jarDirectory url="file://$MODULE_DIR$/lib/printing" recursive="false" />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library>
        <CLASSES>
          <root url="file://$MODULE_DIR$/lib" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
        <jarDirectory url="file://$MODULE_DIR$/lib" recursive="true" />
      </library>
    </orderEntry>
  </component>
</module>