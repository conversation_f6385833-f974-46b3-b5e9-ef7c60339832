#!/bin/bash

# Halt on first error
set -e

if [ "$(id -u)" != "0" ]; then
   echo "This script must be run with root (sudo) privileges" 1>&2
   exit 1
fi

# Console colors
RED="\\x1B[1;31m";GREEN="\\x1B[1;32m";YELLOW="\\x1B[1;33m";PLAIN="\\x1B[0m"

# Statuses
SUCCESS="   [${GREEN}success${PLAIN}]"
FAILURE="   [${RED}failure${PLAIN}]"
WARNING="   [${YELLOW}warning${PLAIN}]"

mask=755

echo -e "Starting install...\n"

# Clear the log for writing
> "${install.log}"

run_task () {
    echo -e "Running $1 task..."
    if [ -n "$DEBUG" ]; then
        "./${project.filename}" $@ && ret_val=$? || ret_val=$?
    else
        "./${project.filename}" $@ &>> "${install.log}" && ret_val=$? || ret_val=$?
    fi

    if [ $ret_val -eq 0 ]; then
        echo -e "   $SUCCESS Task $1 was successful"
    else
        if [ "$1" == "spawn" ]; then
            echo -e "   $WARNING Task $1 skipped.  You'll have to start ${project.name} manually."
            return
        fi
        echo -e "   $FAILURE Task $1 failed.\n\nRe-run with DEBUG=true for more information."
        false # throw error
    fi
}

# Ensure java is installed and working before starting
"./${project.filename}" --version

# Make a temporary jar for preliminary installation steps
run_task preinstall

run_task install --dest "/opt/${project.filename}"

# We should be installed now, generate the certificate
pushd "/opt/${project.filename}" &> /dev/null
run_task certgen

# Tell the desktop to look for new mimetypes in the background
umask_bak="$(umask)"
umask 0002 # more permissive umask for mimetype registration
update-desktop-database &> /dev/null &
umask "$umask_bak"

echo "Installation complete... Starting ${project.name}..."
# spawn itself as a regular user, inheriting environment
run_task spawn "/opt/${project.filename}/${project.filename}"

popd &> /dev/null