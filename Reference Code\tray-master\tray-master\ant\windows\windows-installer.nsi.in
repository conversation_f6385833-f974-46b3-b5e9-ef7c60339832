!define MUI_BGCOLOR "SYSCLR:Window"
!define MUI_TEXTCOLOR "SYSCLR:WindowText"
!include MUI2.nsh
!include x64.nsh
!include LogicLib.nsh

!ifdef NSIS_UNICODE
	!addplugindir "${basedir}/ant/windows/nsis/Plugins/Release_Unicode"
!else
	!addplugindir "${basedir}/ant/windows/nsis/Plugins/Release_ANSI"
!endif
!addincludedir "${basedir}/ant/windows/nsis/Include"
!include FindJava.nsh
!include StdUtils.nsh
!include StrLoc.nsh

Name "${project.name}"
OutFile "${nsis.outfile}"
RequestExecutionLevel admin

!define MUI_ICON "${basedir}/assets/branding/windows-icon.ico"

; Branding for qz only
!if "${project.filename}" == "qz-tray"
    !define MUI_WELCOMEFINISHPAGE_BITMAP "${basedir}\ant\windows\nsis\welcome.bmp"
!endif

!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_LANGUAGE "English"

!macro QzInstaller step option value
    SetDetailsPrint textonly
    DetailPrint "Running ${step}..."
    SetDetailsPrint listonly
    DetailPrint 'Running ${step}: "$java" ${install.opts} -jar "$OUTDIR\${project.filename}.jar" "${step}" "${option}" "${value}"'
    SetDetailsPrint both
    ClearErrors
    nsExec::ExecToLog '"$java" ${install.opts} -jar "$OUTDIR\${project.filename}.jar" "${step}" "${option}" "${value}"'
    Pop $0
    ${If} "$0" != "0"
        Abort "Installation failed during ${step} step.  Please check log for details."
    ${EndIf}
!macroend

!macro VerifyJava jumpto
    ; Test java executable
    nsExec::ExecToLog '"$java" -version"'
    Pop $0
    ${If} "$0" == "0"
        Goto Resume
    ${EndIf}

    ; Handle scenario where Java is bundled but broken
    ${StrLoc} $R1 "$java" "runtime\bin" ">" ; e.g. "nss1234.tmp\payload\runtime\bin\java.exe"
    ${If} $R1 != ""
        SetDetailsPrint both
        Abort  "Sorry, this version of ${project.name} cannot be installed on this system."
    ${EndIf}

    ; Offer to download Java if missing and non-silent install
    ${IfNot} ${Silent}
        MessageBox MB_YESNO "Java is required.  Download now?" IDYES Website IDNO Resume

        ; Visit Java website
        Website:
        ExecShell "open" "${java.download}"
        MessageBox MB_OK "Click OK after Java is installed"

        ; Check again for Java
        Goto ${jumpto}
    ${EndIf}
    Resume:
!macroend

Section
    ; Set environmental variable for silent install to be picked up by Java
    ${If} ${Silent}
      System::Call 'Kernel32::SetEnvironmentVariable(t, t)i ("${vendor.name}_silent", "1").r0'
    ${EndIf}

    ; Echo final destination to logs
    SetOutPath $INSTDIR

    ; Copy files to a temporary location
    SetOutPath "$PLUGINSDIR\payload"
    DetailPrint "Extracting..."
    SetDetailsPrint none ; Temporarily suppress details
    File /r "${dist.dir}\*"

    ; Set the $java variable
    TryAgain:
    Push "$OUTDIR"
    Call FindJava
    !insertmacro VerifyJava "TryAgain"

    ; Run preinstall tasks
    SetDetailsPrint both
    !insertmacro QzInstaller "preinstall" "" ""

    ; Run install tasks
    !insertmacro QzInstaller "install" "--dest" $INSTDIR

    ; Run certgen tasks
    SetOutPath $INSTDIR
    !insertmacro QzInstaller "certgen" "" ""

    ; Launch a non-elevated instance of ${project.name}
    ${StdUtils.ExecShellAsUser} $0 "$INSTDIR\${project.filename}.exe" "open" ""
SectionEnd

!macro Init
    ${If} ${RunningX64}
        SetRegView 64
        ${DisableX64FSRedirection}
    ${EndIf}
    ${If} $INSTDIR == ""
        ${If} ${RunningX64}
            StrCpy $INSTDIR "$PROGRAMFILES64\${project.name}"
        ${Else}
            StrCpy $INSTDIR "$PROGRAMFILES\${project.name}"
        ${EndIf}
    ${EndIf}
!macroend

; Runs for installs
Function .onInit
    !insertmacro Init
FunctionEnd
