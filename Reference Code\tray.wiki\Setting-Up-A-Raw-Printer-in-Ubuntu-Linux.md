### Compatibility

* :white_check_mark: 2.1 | :white_check_mark: 2.0 | :white_check_mark: 1.9 | [...](Roadmap)
* [Windows](setting-up-a-raw-printer-in-windows) | [Mac](setting-up-a-raw-printer-in-osx) | **Linux**

This tutorial is for raw printers _only_. If unsure please see [What is Raw Printing?](what-is-raw-printing)

### Background
These steps will allow a USB, Network, Serial or Parallel attached printer to receive raw commands through Java on Ubuntu Linux.

### Steps

1. Open **Settings > Printers > Additional Printer Settings** 

   <img width="874" alt="Screenshot 2023-01-18 at 2 03 22 AM" src="https://user-images.githubusercontent.com/6345473/213106136-aecb3fbd-cf43-411e-a0d0-3055c66808fe.png">


1. Click **+Add**
1. Set up the printer/port settings:

   #### For USB Printer:
   **Devices:** Enter URI

   **Enter Device URI:** usb://Zebra/LP2844 (example)

     *Get this port by issuing the command* ``lpinfo -v |grep usb:``

     > **Note:** You don't need the "? location=1a200000" information.

   #### For Network Printer:
   **Devices:** AppSocket/HP JetDirect

   **Host:** *************** (example)

   **Port:** 9100

   The Host IP address may be configured to use a different address, which is outside of the scope of this tutorial.  If AppSocket is not listed, close and relaunch the Wizard and it should appear.

   #### For Serial Printer
   **Devices:** Enter URI

   **Enter Device URI:** serial:/dev/ttyS0 (or serial:/dev/ttyUSB0, etc)

   *For USB serial, issue the command* ``dmesg |grep tty``

   **Insufficient permissions fix:** This command grants the logged in user write permission to all serial ports:

   ``sudo usermod -a -G dialout $USER;``

   >**Note:** 12.04 users may also need to remove [ModeManager] (https://bugs.launchpad.net/ubuntu/+source/linux/+bug/662881) due to a specific bug, ttylUSB devices don't seem writable by CUPS, so try ``printToFile('/dev/ttyUSB0');`` instead.

1. Click **Forward**
1. **Driver:** Select printer from Database > Generic > **Forward**
1. **Models:** Raw Queue

   **Drivers:** Generic Raw Queue > **Forward**
    
   <img width="708" alt="image" src="https://user-images.githubusercontent.com/6345473/213106474-1dab8efa-3182-4207-91e0-6659b216b59d.png">

1. **Printer Name:** (ie: `my_raw_printer`)

   **Description:** My Raw Printer

You may now print to your printer as a raw device.

***

To learn how to print from a webpage continue to [Getting Started](getting-started)