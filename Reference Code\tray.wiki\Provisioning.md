### Compatibility

* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :white_check_mark: 2.0 | :no_entry: 1.9 | [...](Roadmap)

## Objective
Since 2.2.4

Provide custom deployment options with QZ Tray

## Quick Start

1. Prepare environment for [compiling](compiling)
2. Prepare `provision.json` file
   ```json
   [
      {
         "description": "Configure log file size",
         "type": "property",
         "data": "log.size=2097152"
      }
   ]
   ```
3. Invoke `ant` with the provision file:
   ```bash
   ant -Dprovision.file=provision.json nsis
   ```
4. You now have a customized installer.

## Provision Types



[CA](#ca) | [Cert](#cert) | [Conf](#conf) | [Property](#property) | [Preference](#preference) | [Script](#script) | [Software](#software) | [Remover](#remover)

### CA
Custom trusted root certificate is installed during the [`"phase": "certgen"`](#phase) and write an `authcert.override=self-signed.crt` entry to `qz-tray.properties` to allow deployment with 3rd-party CA or self-signed certificates.  For trusting certificates issued by QZ Industries, see [`cert`](#cert) provisioning instead.

Since 2.2.5

```json
[
   {
      "description": "Trusts a self-signed certificate",
      "type": "ca",
      "data": "self-signed.crt"
   }
]
```

### Cert
Certificates are installed during the [`"phase": "startup"`](#phase) and write an entry to `allowed.dat` (e.g. `%APPDATA%\qz`) to avoid clicking "Remember this decision".  For trusting self-signed certificates, see [`ca`](#ca) provisioning instead.

```json
[
   {
      "description": "Trusts a certificate",
      "type": "cert",
      "data": "digital-certificate.txt"
   }
]
```

### Conf

Conf settings are installed during [`"phase": "certgen"`](#phase) (post-install) and writes a [properties entry](command-line#system-properties) to `runtime/conf/<path>` located in the installation directory.  This is useful for configuring the proxy or changing local Java security policy, e.g. `net.properties`, `logging.properties`, etc.

Since 2.2.5

```json
[
   {
      "description": "Configure proxy",
      "type": "conf",
      "data": "java.net.useSystemProxies=true",
      "path": "net.properties"
   }
]
```

### Property
Properties are installed during [`"phase": "certgen"`](#phase) (post-install) and writes a [properties entry](command-line#system-properties) to `qz-tray.properties` located in the installation directory. (e.g. `%PROGRAMFILES%\QZ Tray`)

```json
[
   {
      "description": "Disable logs",
      "type": "property",
      "data": "log.disable=true"
   }
]
```

* ‡ `websocket.secure.ports` and `websocket.insecure.ports` are an exception and must also be provided during [`"phase": "install"`](#phase) for Windows installers to set firewall rules.

### Preference
Properties are installed during [`"phase": "startup"`](#phase) and write a [properties entry](command-line#system-properties) to `prefs.properties` located in the user data directory (e.g. `%APPDATA%\qz`)

```json
[
   {
      "description": "Show all notifications",
      "type": "preference",
      "data": "tray.notifications=true"
   }
]
```

### Script
Script are run during [`"phase": "install"`](#phase) or [`"phase": "startup"`](#phase) and allow granular customizations.  The script will be automatically copied into the installer.
* `data` Must point to a valid script file and may be relative or absolute in respect to the location of `provision.json`.
* `os` is recommended and never inferred.
* The script may reference custom [environment variables](#script-variables) provided by the installer.

```json
[
   {
      "description": "Python script",
      "os": "linux|mac",
      "type": "script",
      "data": "my_script.py"
   }
]
```

#### Script Variables

| Name          | Example       | | Name                | Example                              |
|---------------|---------------|-|---------------------|--------------------------------------|
| `APP_TITLE`   |  QZ Tray      | | `APP_VENDOR_ABBREV` | qz                                   |
| `APP_ABBREV`  | qz-tray       | | `APP_VENDOR`        | QZ Industries, LLC                   |    
| `APP_VERSION` | 2.2.4         | | `APP_SHARED_DIR`    | C:\ProgramData\qz                    |
| `APP_OS`      |  windows      | | `APP_DIR`           | C:\Program Files\QZ Tray             |
| `APP_ARCH`    | x86_64        | | `APP_USER_DIR`      | C:\Users\<USER>\AppData\Roaming\qz |


```python
#!/usr/bin/env python3

import os

title=os.getenv('APP_TITLE')
version=os.getenv('APP_VERSION')

def notify(title, message):
  os.system(f"notify-send '{title}' '{message}'")

notify(title, "Hello from {} {}".format(title, version))

```

### Software
Software is run during [`"phase": "install"`](#phase) and will execute with the provided deployment arguments.
* `os` optional but will be assumed based on file extension
* `arch` is optional but strongly recommended
* `args` will automatically be split up on whitespace for convenience
   * ... if whitespace is needed, use `"arg1": "first arg"`, `"arg2": "second arg"`

| Extension | Platform | Supported |
|-----------|----------|-----------|
| `.exe`, `.msi` | [Windows](#os) | :white_check_mark: |
| `.pkg`         | [macOS](#os) | :white_check_mark: |
| `.dmg`         | [macOS](#os) | :warning: |
| `.run`         | [Linux](#os) | :white_check_mark: |


```json
[
   {
      "description": "Install Notepad++",
      "type": "software",
      "os": "windows",
      "arch": "x86_64",
      "data": "npp.8.5.8.Installer.x64.exe",
      "args": "/S"
   }
]
```

### Remover
Remover is a task to uninstall other QZ Tray installations, which is especially helpful for [Company Branded](https://qz.io/docs/company-branded#build-qz-tray) builds to remove known-conflicting installs.
* `data` may be `qz`, or the application identifiers from the [Company Branded](https://qz.io/docs/company-branded#build-qz-tray) portal.
  * Example: `"data": "qz"` Shorthand for QZ branded installers.
  * Example: `"data": "Cherry Connect,cc-util,cc"` Long format, required for Company Branded builds.

```json
[
   {
      "description": "Remove QZ Tray",
      "type": "remover",
      "data": "QZ Tray,qz-tray,qz",
   }
]
```

## Phase

| Phase | Description |
|-------|-------------|
| `"phase": "install"` | Runs as a privileged account during QZ Tray installation, but **before** QZ Tray has been fully deployed to the system. |
| `"phase": "certgen"` | Runs as a privileged account during QZ Tray installation, but **after** QZ Tray has been fully deployed to the system. |
| `"phase": "startup"` | Runs as user account during the startup of QZ Tray. |
| `"phase": "uninstall"` | Runs as as a privileged account during uninstallation of QZ Tray.|


## Arch

Arch will designate which architecture to perform the step. 
* If not provided, `arch` will default to `"arch": "*"` and run on all systems.
* Multiple values may be provided: `"arch": "aarch64|riscv64"`

| Arch | Description | Supported |
|------|-------------|-----------|
| `"arch": "x86_64"` | Intel 64-bit systems | :white_check_mark: |
| `"arch": "aarch64"` | Apple Silicon or ARM64 systems | :white_check_mark: |
| `"arch": "riscv64"` | RISC-V 64-bit systems | :white_check_mark: |
| `"arch": "arm"` | ARM 32-bit systems | :warning: |
| `"arch": "x86"` | Intel 32-bit systems | :warning: |

## OS

OS will designate which platform to perform the step. 
* If not provided, `os` will default to `"os": "*"` and run on all systems
   * Except [`software`](#software) installers, which will try to choose a sane value.
* Multiple values may be provided: `"os": "mac|linux"`

| OS | Description | Supported |
|------|-------------|-----------|
| `"os": "windows"` | Windows Operating System | :white_check_mark: |
|  `"os": "mac"` | macOS Operating System | :white_check_mark: |
| `"os": "linux"` | Linux Operating System | :white_check_mark: |