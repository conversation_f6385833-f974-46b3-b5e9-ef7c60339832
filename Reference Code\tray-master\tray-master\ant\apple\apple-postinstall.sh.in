#!/bin/bash

# Halt on first error
set -e

# Get working directory
DIR=$(cd "$(dirname "$0")" && pwd)
pushd "$DIR/payload/${project.name}.app/Contents/MacOS/"

./"${project.name}" install >> "${install.log}" 2>&1
popd

# Use install target from pkgbuild, an undocumented feature; fallback on sane location
if [ -n "$2" ]; then
    pushd "$2/Contents/MacOS/"
else
    pushd "/Applications/${project.name}.app/Contents/MacOS/"
fi

./"${project.name}" certgen >> "${install.log}" 2>&1

# Start qz by calling open on the .app as an ordinary user
su "$USER" -c "open ../../" || true