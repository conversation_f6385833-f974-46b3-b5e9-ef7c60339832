### Compatibility

* :hourglass: 2.1 | :no_entry: 2.0 | :no_entry: 1.9 | [...](Roadmap)

### Objective

Add LetsEncrypt certificate support to QZ Tray.

### Prerequisites

* QZ Tray 2.1 with `certgen` command line option (See [#504](https://github.com/qzind/tray/pull/504))
* Unix machine with `certbot` installed
* Internet-accessible machine
   * For a basic (e.g. home/small business) router, this is usually involves:
       * Static IP for the machine running QZ Tray
       * Port forwarding enabled for ports `80`, `8181` and optionally `8182`
* A fully qualified DNS name pointing to this machine
   * For testing purposes, you may use a free DNS service such as https://www.duckdns.org/.

### Generate Certificate

QZ Tray is capable of renewing a LetsEncrypt certificate but it must be in a writable location.  First step is to generate the initial certificate.

> :warning: **Mac Users:** The below commands were written for Linux, however this tutorial will work on MacOS if `/srv/qz/ssl` is replaced with `/Library/Application\ Support/qz` and replace `/opt/qz-tray/qz-tray` with `/Applications/QZ\ Tray/Contents/MacOS/QZ\ Tray`.

> :warning: **White-Label Users:** If using a white-labeled version of QZ Tray, make sure to replace all instance of `qz`, `qz-tray`, or `QZ Tray` with your [`branded`](white-labeling) value.

1. Ask certbot to write files to the QZ shared directory:
   ```bash
   certbot certonly --standalone --config-dir /srv/qz/ssl/letsencrypt/config --logs-dir /srv/qz/ssl/letsencrypt/logs --work-dir /srv/qz/ssl/letsencrypt/
   ```

   This will install your certificate into `/srv/qz/ssl/letsencrypt/live/<hostname>`.

2. Using the `certgen` command, tell QZ Tray to reconfigure using these files, updating `<hostname>` with your domain name.
   ```bash
   sudo /opt/qz-tray/qz-tray certgen --key /srv/qz/ssl/letsencrypt/live/<hostname>/privkey.pem --cert /srv/qz/ssl/letsencrypt/live/<hostname>/fullchain.pem
   ```

3. Start QZ Tray

### Modify Code

By default QZ Tray will attempt to connect to `localhost`.  To force QZ Tray to connect to the [Print-Server](Print-Server) add the `{ host: '<hostname>' }` parameter:

```js
qz.websocket.connect({ host: '<hostname>' });
```

### Renew Certificate

Five days before the LetsEncrypt certificate expires, QZ Tray will call `certbot` using the above path and automatically attempt to renew the certificate.

### Uninstall LetsEncrypt

1. Delete the certificate
   ```bash
   certbot delete --cert-name <hostname> --config-dir /srv/qz/ssl/letsencrypt/config --logs-dir /srv/qz/ssl/letsencrypt/logs --work-dir /srv/qz/ssl/letsencrypt/
   ```
2. Delete the `letsencrypt` folder
   ```bash
   rm -rf /srv/qz/ssl/letsencrypt
   ```
3. Reset QZ Tray to use a self-signed certificate.
   ```bash
   sudo /opt/qz-tray/qz-tray certgen
   ```
4. Start QZ Tray 