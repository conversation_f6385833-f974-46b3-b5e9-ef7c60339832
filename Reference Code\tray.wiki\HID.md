### Compatibility

* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :white_check_mark: 2.0.1 | :no_entry: 2.0 | :no_entry: 1.9 | [...](Roadmap)

### Summary

Read and write data to an attached HID device.

**Note:** For raw USB support, see [USB Communication](usb) instead. (HID is strongly recommended if available)

### Read Data
 * Claims device based on hardware information using [`claimDevice(vendorId, productId)`](https://qz.io/api/qz.hid#.claimDevice)
 * Reads data from device using [`readData(vendorId, productId, responseSize)`](https://qz.io/api/qz.hid#.readData)
 * Releases device using [`releaseDevice(vendorId, productId)`](https://qz.io/api/qz.hid#.releaseDevice)

```js
// Hardware info (modify to match hardware)
var usb = {
   vendor: '0x0EB8',
   product: '0xF000'
};

// Generic error handler
var err = function(e) { console.error(e); }

// Generic data handler
var process = function(data) { console.log(data); }

// Handler to release claimed device	
var release = function() {
   qz.hid.releaseDevice(hid.vendor, hid.product).catch(err);
}

// Connect to QZ Tray, claim, read, release
qz.websocket.connect().then(function() {
   return qz.hid.claimDevice(hid.vendor, hid.product);
}).then(function() {
   return qz.hid.readData(hid.vendor, hid.product, '8'); // *
}).then(process).then(release).catch(err);

// Note:  Some hardware such as Fairbanks scales use '6' for byte length.  Adjust as needed
```

### Send Data
* Send data to a claimed HID device using [`sendData(vendorId, productId, data)`](https://qz.io/api/qz.hid#.sendData)
```js
qz.hid.sendData('0x0EB8', '0xF000', data).catch(function(e) { console.error(e); });
```


### Honeywell Barcode Scanner

```js
var device = {
   vendorId: '0x0c2e',     // Honeywell barcode scanner
   productId: '0x0b87',
   readSize: 20,            // in bytes, adjust as needed
   usagePage: '0x008c',    // Mandatory for HID devices with duplicate endpoints
                           // Note:  usagePage is not yet available for Linux
};

qz.websocket.connect().then(function () {
   qz.hid.claimDevice(device);
}).then(function () {
   qz.hid.openStream(device);
}).catch(function (e) {
   console.error(e);
});

qz.hid.setHidCallbacks(streamEvent => {
    if(streamEvent.type === 'RECEIVE') {
        let barcode = '';
        streamEvent.output.forEach((item) => barcode += String.fromCharCode(parseInt(item, 16)));
        console.log(barcode);
    } else {
        console.error(streamEvent.exception);
    }
});
```

### Posiflex USB Cash Drawer

This code was contributed by a community member and is untested.  Also please see [`#619`](https://github.com/qzind/tray/issues/619) about data length limitations.

```js
// Posiflex Cash Drawer
var usb = {
   vendorId: '0x0d3a',
   productId: '0x0207'
};

// Drawer number 0-7 (7 is default)
var drawer = String.fromCharCode(7);
//                               ^-- Drawer number

/**
   USB kick code for the posiflex 4000 series:
   - byte[0] and byte[1] are both the drawer number
   - the drawers can be assigned a number 0-7 (default is 7)
**/

var data = '\x07\x07';

// Connect to QZ Tray, claim, write, release
qz.websocket.connect().then(function() {
   return qz.hid.claimDevice(hid.vendor, hid.product);
}).then(function() {
   return qz.hid.sendData(hid.vendor, hid.product, data);
}).catch(function(sendErr) {
   console.error(sendErr);
}).then(function() {
    return qz.hid.releaseDevice(hid.vendor, hid.product);
}).catch(function(releaseErr) {
   console.error(sendErr);
});
        