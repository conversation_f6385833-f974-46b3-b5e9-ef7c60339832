### Compatibility

* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :no_entry:   2.0 | :no_entry: 1.9 | [...](Roadmap)

### Background

Provide an interface for two-way file communication between QZ Tray and the local file system.

### Prerequisites

For security reasons, File Communication will only work if messages are [signed](signing).  To disable this behavior, set the property `security.file.strict=false` in `prefs.properties` or `qz-tray.properties` and restart QZ Tray.

### File Operations
* [`READ`](#read-data), [`WRITE`](#write-data), [`APPEND`](#append-data), [`DELETE`](#delete-file), [`LIST`](#list-files), [`LISTEN`](#listen-for-changes)

### Local Storage

By default, data is stored local to the logged-in user next to the QZ Tray logs in a folder called `sandbox/<CN>` where `<CN>` is the `Common Name` from the [`digital-certificate.txt`](#generate-certificate).  Folders may contain `^` to escape special characters, such as `One/Half Inc` would be `One^fHalf Inc` on the filesystem.

* The default data storage can be changed to all desktop users with the param `shared: true`.
* To disable the `sandbox/<CN>` suffix, use the param `sandbox: false`.

|  | :bust_in_silhouette: User | :bust_in_silhouette: + :lock: User Sandbox | :busts_in_silhouette: Shared |  :busts_in_silhouette: + :lock: Shared Sandbox |
|---|------|--------|---------------|---------------|
| **Windows** | `%AppData%/qz/shared` | `%AppData%/qz/sandbox/<CN>` | `%ProgramData%/qz/shared` | `%ProgramData%/qz/sandbox/<CN>` |
| **MacOS** | `~/Library/Application Support/qz/shared` | `~/Library/Application Support/qz/sandbox/<CN>` | `/Library/Application Support/qz/shared` | `/Library/Application Support/sandbox/<CN>`
| **Linux** | `~/.qz/shared` | `~/.qz/sandbox/<CN>` | `/srv/qz/shared/` | `/srv/qz/sandbox/<CN>` |

### Custom Storage Locations

**Custom Shared Locations**

Custom [local storage](#local-storage) locations may be added via `qz-tray.properties`.
 * Paths are separated with a semicolon `;`
 * A semicolon `;` is a special delimiter and must be  escaped as `^;` if exist within a path or `<CN>` field.

```properties
file.whitelist=C:\\FirstFolder\\;C:\\SecondFolder\\
```
 * Note when listening to a shared drive, UNC paths (e.g. `\\\\server\\share` appear to work better than mapped drives, e.g. `G:\\`)
 * Restart QZ Tray for changes to take effect.

**Custom Sandbox Locations**

 * If followed by a pipe character `|`, the folder behaves as a sandboxed location.  The content after the pipe character `|` will be the `<CN>`
 * A pipe `|` is a special delimiter and must be  escaped as `^|` if exist within a path or `<CN>` field

```properties
file.whitelist=C:\\FirstFolder\\|ABC Inc.;C:\\SecondFolder\\|XYZ Inc.
```

### Read Data

Read data via [`qz.file.read(...)`](https://qz.io/api/qz.file#.read) from a shared location and display it to the console.

<details>
 <summary>Parameters</summary>

* Parameter [`path`](https://qz.io/api/qz.file#.read) can be absolute or relative file
* Parameter [`sandbox: false`](https://qz.io/api/qz.file#.read) will prevent appending `sandbox/<CN>` to file names; an optional security measure to avoid other trusted sites from reading from each other's folders.
* Parameter [`shared: true`](https://qz.io/api/qz.file#.read) will use a [shared location](#local-storage) accessible to all computer users
* Parameter [`flavor: plain|base64|hex`](https://qz.io/api/qz.file#.read) Since 2.2.2 will affect the return format of the data
</details>

```js
qz.file.read('file.txt', { sandbox: false, shared: true }).then(function(data) {
   console.log(data);
}).catch(function(err) {
   console.error(err);
});
```

### Write Data

Write the specified data via [`qz.file.write(...)`](https://qz.io/api/qz.file#.write) to the specified shared location, overwriting if exists.  Will create any parent directories as needed.  Path `'file.txt'` would normally be relative path.  Absolute file paths are allowed so as long as they're part of the approved [local storage](#local-storage) locations.

```js
qz.file.write('file.txt', { sandbox: false, shared: true, data: "Hi!\n" }).then(function() {
   console.log("OK");
}).catch(function(err) {
   console.error(err);
});
```

#### Append Data
Use [`append: true`](https://qz.io/api/qz.file#.write) to bypass default overwrite file behavior.  Path `'file.txt'` would normally be relative path.  Absolute file paths are allowed so as long as they're part of the approved [local storage](#local-storage) locations.

```js
qz.file.write('file.txt', { sandbox: false, shared: true, append: true, data: "There!\n" }).then(function() {
   console.log("OK");
}).catch(function(err) {
   console.error(err);
});
```

### List Files

List files within the specified folder [`qz.file.list(...)`](https://qz.io/api/qz.file#.list).  Path `'.'` must be a folder and normally would be a relative path.  Absolute file paths are allowed so as long as they're part of the approved [local storage](#local-storage) locations.

```js
qz.file.list('.', { sandbox: false, shared: true }).then(function(data) {
   console.log("Files:");
   for (var n = 0; n < data.length; n++) {
      console.log(data[n]);
   }
}).catch(function(err) {
   console.error(err);
});
```

### Delete File
Delete the specified file or folder via [`qz.file.remove(...)`](https://qz.io/api/qz.file#.remove).  Path `'file.txt'` normally would be a relative path.  Absolute file paths are allowed so as long as they're part of the approved [local storage](#local-storage) locations.

```js
qz.file.remove('file.txt', { sandbox: false, shared: true }).then(function(data) {
   console.log("OK");
}).catch(function(err) {
   console.error(err);
});
```

### Listen For Changes

Listens for file change events within the specified folder [`qz.file.startListening(...)`](https://qz.io/api/qz.file#.startListening).  Path `'.'` must be a folder and normally would be a relative path.  Absolute file paths are allowed so as long as they're part of the approved [local storage](#local-storage) locations.  Some applications will fire duplicate events for Windows.  This is a [limitation of WatchService on Windows](https://stackoverflow.com/questions/16777869).

<details>
 <summary>Parameters</summary>

* Parameter [`include: []`](https://qz.io/api/qz.file#.startListening) Files to include when listening. (Since 2.1.1)
* Parameter [`exclude: []`](https://qz.io/api/qz.file#.startListening) Since 2.1.1: Files to exclude when listening. (Since 2.1.1)
* Parameter [`caseSensitive: true`](https://qz.io/api/qz.file#.startListening) Since 2.1.1: Whether to use case-sensitive file matching. (Since 2.1.1)
* Parameter [`listener`](https://qz.io/api/qz.file#.startListening) set to `null` to prevent file data from being returned along with file change events.
   * Parameter [`listener.lines: 10`](https://qz.io/api/qz.file#.startListening) max number of lines to read at a time. `-1` to disable.
   * Parameter [`listener.bytes: 4048`](https://qz.io/api/qz.file#.startListening) max number of bytes to read at a time. `-1` to disable.
   * Parameter [`listener.reverse: true`](https://qz.io/api/qz.file#.startListening) toggles whether to read from the bottom of the file.
</details>

```js

// Setup our callback for when an event occurs
qz.file.setFileCallbacks(function(streamEvent) {
   if (streamEvent.type !== 'ERROR') {
      console.log("Type: " + streamEvent.eventType);
      console.log("File: " + streamEvent.file);
      // Data is optional, toggled off by setting params to null
      console.log("Data: " + streamEvent.fileData ? streamEvent.fileData : "<none>");
   } else {
      console.error(streamEvent.message);
   }
});

// Fetch up to 10 line changes at a time, reading from the bottom
var params = { sandbox: false, shared: true, listener: { lines: 10, reverse: true } };

qz.file.startListening('.', params).then(function() {
   console.log('OK');
}).catch(function(err) {
   console.error(err);
});
```

#### Filter Results
Since 2.1.1, the `{ include: [], exclude: [] }` parameters are supported to filter the files returned when listening.
```js
// Only listen to 'foo*.txt' files, unless they're called 'foobar*.txt'
var params = { include: 'foo*.txt', exclude: 'foobar*.txt' };
// Providing multiple filters
// var params = { include: ['foo*.txt', 'foo*.log'], exclude: ['foobar*.txt', 'foobar*.log'] };

qz.file.startListening('.', params).then(function() {
   console.log('OK');
}).catch(function(err) {
   console.error(err);
});
```

#### Stop Listening

Stops listening for file change events for the specified folder [`qz.file.stopListening(...)`](https://qz.io/api/qz.file#.stopListening).  Path `'.'` must be a folder and normally would be a relative path.  Absolute file paths are allowed so as long as they're part of the approved [local storage](#local-storage) locations.

```js
qz.file.stopListening('.', { sandbox: false, shared: true }).then(function() {
   console.log('OK');
}).catch(function(err) {
   console.error(err);
});
```

#### Stop All Listeners
Stops listening for all file change events [`qz.file.stopListening()`](https://qz.io/api/qz.file#.stopListening) for the current, active websocket connection.  If running QZ Tray in [print-server](print-server) mode, concurrent but separate clients will remain actively listening.

```js
qz.file.stopListening().then(function() {
   console.log('OK');
}).catch(function(err) {
   console.error(err);
});