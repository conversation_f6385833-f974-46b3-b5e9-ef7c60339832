### Compatibility

* :white_check_mark: 2.1 | :white_check_mark: 2.0 | :white_check_mark: 1.9 | [...](Roadmap)

### Background
Fonts may need to be installed on a Zebra printer to print special characters, such as those found in Asian fonts. Fonts are added to the printer along with encoding files using a direct communication method such as the Zebra Setup Utilities.

### Using Zebra Setup Utilities to Install Fonts
To install files on a Zebra printer, the [Zebra Setup Utilities (ZSU)](https://www.zebra.com/us/en/support-downloads/printer-software/printer-setup-utilities.html) (Windows only) may be used to send files and communicate directly to the printer.

### Requirements:
- Windows PC
- Zebra Printer capable of installing fonts
- Download and install [Zebra Setup Utilities](https://www.zebra.com/us/en/support-downloads/printer-software/printer-setup-utilities.html)
- Supported Font Pack (such as Andale) -- this includes:
    - `.ZPL` font file - Zebra Printing Language file, sent to the printer and stored in Flash memory
    - `.TTF` font file - True Type Font file, installed on the PC
    - `.ZSE` encoding file - Zebra Encoding File, sent to the printer and stored in Flash memory


### Steps:
Install and use the Andale Simplified Chinese font (provided as an example)
1. Open the **Zebra Setup Utilities** software.
2. Select the **desired printer** from the Installed Printer list.
3. Select the **Open Printer Tools** button.
4. On the **Tools** window, select the **Action** tab.
5. From the list of actions, select **Send File**. When selected, the Command Description updates and a text box with a **browse button (...)** displays.
6. Select the **browse button (...)** to navigate to the location of the font pack. Select the Encoding file (`.ZSE`) to send and select the Open button to populate the path in the text box.
    * `GB.ZSE` for Simplified Chinese
7. Select the **Send** button to send the file to the printer.
8. Repeat Steps 6 and 7 to send the `.ZPL` font file.
    * `Andale_simplified.ZPL` for Simplified Chinese
9. Install the `.TTF` font file on the PC.
    * `ANMDS.TTF` for Andale Simplified Chinese (Example)
10. Verify the files were sent to the printer correctly by opening **direct communication** with the printer and sending the following ZPL command string to return the entire `E:` Flash memory contents: `^XA^HWE:*.*^XZ`
    > **Note:** The Zebra Setup Utilities may be used for direct communication by selecting the **Open Communication With Printer** button from the main screen. This opens the **Direct Communication** window, which is divided into an **upper** and lower window. Enter the command in the **upper** window, select the **Send to Printer** button (or press <kbd>F5</kbd> on the keyboard), and the results of the command are displayed in the **lower** window.
11. Once the files are added to the printer, the font needs a letter/numeric font assignment to be used in the ZPL format for printing. To do this, use the Font Assignment command, `^CW`.
    * Open direct communication with the printer and send the following ZPL command string: `^XA^CW1,E:ANMDS.TTF ^XZ`. Replace the `1` after `^CW` with your desired letter/number reference.
        > **Note:** The printer will not permanently store the Font Assignment command (`^CW`) or Encoding table command (`^SE`) -- if the printer powers off, these settings are lost. Send both of these commands at the beginning of every label.  (Alternately, you can use an Autoexec file, which automatically sends these settings when the printer is powered on: `^XA^DFE:AUTOEXEC.ZPL^CW1,E:ANMDS.TTF^SEE:GB.DAT^XZ`)
12. With the font and encoding file installed on the printer, you're ready for a test print with QZ Tray. Head over to our [Raw Encoding page to view Simplified Chinese ZPLII example code](https://qz.io/wiki/raw-encoding#zpl-simplified-chinese).  With minor adjustments, this example can be modified to print other fonts you've installed.