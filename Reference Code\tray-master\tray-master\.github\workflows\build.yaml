name: build

on: [push, pull_request]

jobs:
  ubuntu:
    runs-on: [ubuntu-latest]
    strategy:
      matrix:
        java: [11, 21]
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-java@v3
        with:
          java-version: ${{ matrix.java }}
          distribution: 'liberica'
      - run: sudo apt-get install nsis makeself
      - run: ant makeself
      - run: sudo out/qz-tray-*.run
      - run: /opt/qz-tray/qz-tray --version
      - run: ant nsis

  macos:
    runs-on: [macos-latest]
    strategy:
      matrix:
        java: [11, 21]
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-java@v3
        with:
          java-version: ${{ matrix.java }}
          distribution: 'liberica'
      - run: brew install nsis makeself
      - run: ant pkgbuild
      - run: echo "Setting CA trust settings to 'allow' (https://github.com/actions/runner-images/issues/4519)"
      - run: security authorizationdb read com.apple.trust-settings.admin > /tmp/trust-settings-backup.xml
      - run: sudo security authorizationdb write com.apple.trust-settings.admin allow
      - run: sudo installer -pkg out/qz-tray-*.pkg -target /
      - run: echo "Restoring CA trust settings back to default"
      - run: sudo security authorizationdb write com.apple.trust-settings.admin < /tmp/trust-settings-backup.xml
      - run: "'/Applications/QZ Tray.app/Contents/MacOS/QZ Tray' --version"
      - run: ant makeself
      - run: ant nsis

  windows:
    runs-on: [windows-latest]
    strategy:
      matrix:
        java: [11, 21]
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-java@v3
        with:
          java-version: ${{ matrix.java }}
          distribution: 'liberica'
      - run: choco install nsis
      - run: ant nsis
      - run: Start-Process -Wait ./out/qz-tray-*.exe -ArgumentList "/S"
      - run: "&'C:/Program Files/QZ Tray/qz-tray.exe' --wait --version|Out-Null"
