### Compatibility
* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :white_check_mark: 2.0 | :white_check_mark: 1.9 | [...](Roadmap)

### Objective
Perform a silent, unattended or slipstream installation of QZ Tray, such as in a corporate environment.
* **Note** For continuous-integration (CI/CD) or quick-installation to a developer's machine, you may be interested in utilizing [`qz.sh`](#qzsh) instead, which will automatically connect to the download page and fetch the latest `stable` release.
   * :bulb: CI/CD consideration: Due to environment differences,  knowledge of [[starting QZ Tray manually|command-line]] and handling [[headless operation|headless]] is a prerequisite.

### Steps

#### Windows

We use [NSIS](http://nsis.sourceforge.net/Docs/Chapter3.html) (Nullsoft Scriptable Installer System) for Windows.

 * Install silently to the default location (run Command Prompt as Administrator)
   ```bat
   start /wait "" qz-tray-2.x.x.exe /S
   ```

 * Install silently to a non-standard location (run Command Prompt as Administrator)
   ```bat
   start /wait "" qz-tray-2.x.x.exe /S /D=C:\Program Files\QZ Tray
   ```

#### Linux

We use [Makeself](http://stephanepeter.com/makeself/) for Linux.
 * Install silently to the default location

   ```bash
   sudo bash qz-tray-2.x.x.run -- -y
   ```

#### Mac

We can use the `installer` command on Mac to silently install QZ Tray via the command line.  Type [(or click here): `man installer`](https://developer.apple.com/library/mac/documentation/Darwin/Reference/ManPages/man8/installer.8.html) on the command line for more examples.

* Install silently to the default location
  ```bash
  sudo installer -pkg qz-tray-2.x.x.pkg -target /
  ```

* Install silently to a non-standard location
  > **Note:**  Use at your own risk; Non-standard install locations are currently not supported.

  ```bash
  sudo installer -pkg qz-tray-2.x.x.pkg -target /Alternate/Volume
  ```

### Uninstall

#### Windows Uninstall

We use [NSIS](http://nsis.sourceforge.net/Docs/Chapter3.html) (Nullsoft Scriptable Installer System) for Windows.

 * Uninstall silently
   ```bat
   start /wait "" "%programfiles%\QZ Tray\uninstall.exe" /S
   ```

#### Linux Uninstall

* Uninstall silently

   ```bash
   sudo bash /opt/qz-tray/uninstall
   ```

#### Mac Uninstall

* Uninstall silently
  ```bash
  # 2.2+
  sudo bash /Applications/QZ\ Tray.app/Contents/Resources/uninstall

  # 2.1 and older
  sudo /Applications/QZ\ Tray.app/Contents/uninstall
  ```


### Automatic Start

<details>
<summary>Automatic start steps (legacy versions only)</summary>

#### QZ Tray 2.1+

QZ Tray 2.1 and higher will automatically start at login, no steps are needed.

#### QZ Tray 2.0

For versions 2.0 and older, the user must click "Automatically start" in the tray icon, which may not be desired for corporate environments.

There are several methods for making an application launch automatically on various desktops, however to mimic the technique used by QZ Tray 1.9 - 2.0 is:

#### Windows Automatic Start

1. First, uncheck "Automatically start" and close QZ Tray.
2. Run the following command through a Command Prompt window.  Administrative access is not required.

    ```cmd
    reg.exe add HKCU\Software\Microsoft\Windows\CurrentVersion\Run\ /v "QZ Tray" /d "%programfiles%\QZ Tray\qz-tray.exe" /f
    ```

    * For [white-label](https://qz.io/wiki/white-labeling) customers, replace `QZ Tray` with the name of your application, e.g. `Acme Printing`, etc.
3. Re-launch QZ Tray and verify the "Automatically start" option has been reselected.

    ![screen shot 2017-12-18 at 12 52 33 pm](https://user-images.githubusercontent.com/6345473/34120061-5c8885c6-e3f2-11e7-824d-0a137919b1dd.png)
4. To secure this change (user cannot disable) requires modifying registry permissions [using `regini` and a separate `.reg` file](https://support.microsoft.com/en-us/help/264584/) which can have other negative side-effects and is out of scope of this tutorial.
</details>

---

## `qz.sh`
(or `pwsh.sh` for Windows)

For quick installation of the latest version, qz offers an online scripted installer.

<img width="550" alt="Screen Shot 2022-04-19 at 1 30 32 PM" src="https://user-images.githubusercontent.com/6345473/164061886-7f9fac10-3f40-483c-9f1f-031bd88acea1.png">

## macOS

```bash
# Download and install the latest stable release of QZ Tray
curl qz.sh |bash
```

## Linux

```bash
# Download and install the latest stable release of QZ Tray
wget -O - qz.sh |bash
```

## Windows

```powershell
# Download and install the latest stable release of QZ Tray
irm pwsh.sh | iex
```

<details>
<summary>Click to expand what to do if you recieve a security error</summary>

```powershell
# Optional: Needed to run a remote script the first time
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser
```
</details>
   

## Advanced

Additional parameters can be provided to specify `beta` or an exact tagged release.
**Note:** There may not be a `beta` version available.  Check https://qz.io/download if unsure.

### Curl
```bash
curl qz.sh |bash -s -- "beta"   # latest beta release
curl qz.sh |bash -s -- "2.2.1"  # tagged "v2.2.1" release
```

### Wget
```bash
wget -O - qz.sh |bash -s -- "beta"   # latest beta release
wget -O - qz.sh |bash -s -- "2.2.1"  # tagged "v2.2.1" release
```

### PowerShell
```powershell
& ([scriptblock]::Create((irm pwsh.sh))) beta   # latest beta release
& ([scriptblock]::Create((irm pwsh.sh))) 2.2.1  # tagged "v2.2.1" release
```

---

## Troubleshooting

Steps for gathering additional logs from a failed installation.

### macOS

* macOS will write the debug installation logs to `/tmp`.
    ```bash
    cat /tmp/qz-installer.log
    # For Company Branded versions:
    # cat /tmp/<vendor_abbreviation>-install.log
    ```

### Linux

* Linux will write the debug installation logs to the terminal if environment variable `DEBUG` is set.
    ```bash
    sudo bash # or su
    export DEBUG=true
    ./qz-tray-x.x.x.run
    # Detailed logs should appear in the console
    ```

### Windows

* Windows does not have a dedicated logging location other than the NSIS installation Window.
* To obtain logs from the NSIS installation Window, start the installation interactively, click the <kbd>Show details</kbd> button and copy/paste the installation logs to a text editor such as Visual Studio Code.  If you believe your error may occur during the startup process, you may run `qz-tray-console.exe` from `C:\Program Files\QZ Tray` for a live,m interactive log.

### Additional Troubleshooting

* If the issue with installation is suspected to be certificate-related, you may re-run the [`certgen`](print-server#remove-localhost-bindings) step interactively.
    ```bash
    # macOS
    sudo "/Applications/QZ Tray.app/Contents/MacOS/QZ Tray" certgen

    # Windows (CMD as Administrator)
    cd "%PROGRAMFILES%\QZ Tray"
    qz-tray-console.exe certgen
    
    # Linux
    sudo /opt/qz-tray/qz-tray certgen
    ```