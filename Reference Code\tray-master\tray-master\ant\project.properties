vendor.name=qz
vendor.company=QZ Industries, LLC
vendor.website=https://qz.io
vendor.email=<EMAIL>

project.name=QZ Tray
project.filename=qz-tray
project.datadir=qz

install.opts=-Djna.nosys=true
launch.opts=-Xms512m ${install.opts}
install.log=/tmp/${project.datadir}-install.log
# jdk9+ flags
# - Dark theme requires workaround https://github.com/bobbylight/Darcula/issues/8
launch.jigsaw=--add-exports java.desktop/sun.swing=ALL-UNNAMED
launch.overrides=QZ_OPTS

src.dir=${basedir}/src
out.dir=${basedir}/out
build.dir=${out.dir}/build
dist.dir=${out.dir}/dist

sign.lib.dir=${out.dir}/jar-signed

jar.compress=true
jar.index=true

# Separate native lib resources from jars
separate.static.libs=true

# See also qz.common.Constants.java
javac.source=11
javac.target=11
java.download=https://bell-sw.com/pages/downloads/#/java-11-lts

# Java vendor to bundle into software (e.g. "*BellSoft|Adoptium|Microsoft|Amazon|IBM")
jlink.java.vendor="BellSoft"
# Java vendor to bundle into software (e.g. "11.0.17+7")
jlink.java.version="11.0.27+9"
# Java garbage collector flavor to use (e.g. "hotspot|openj9")
jlink.java.gc="hotspot"
# Java garbage collector version to use (e.g. openj9: "0.35.0", zulu: "11.62.17")
jlink.java.gc.version="gc-ver-is-empty"
# Bundle a locally built copy of Java instead
# jlink.java.target=/path/to/custom/jdk-x.x.x

# Skip bundling the java runtime
# jre.skip=true

# JavaFX version
javafx.version=19_monocle
javafx.mirror=https://download2.gluonhq.com/openjfx

# Provisioning
# provision.file=${basedir}/provision.json
provision.dir=${dist.dir}/provision

# Mask tray toggle (Apple only)
java.mask.tray=true

# Workaround to delay expansion of $${foo} (e.g. shell scripts)
dollar=$
