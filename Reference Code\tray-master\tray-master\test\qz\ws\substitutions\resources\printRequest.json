{
  "call": "print",
  "params": {
    "printer": {
      "name": "PDFwriter"
    },
    "options": {
      "bounds": null,
      "colorType": "color",
      "copies": 1,
      "density": 0,
      "duplex": false,
      "fallbackDensity": null,
      "interpolation": "bicubic",
      "jobName": null,
      "legacy": false,
      "margins": 0,
      "orientation": null,
      "paperThickness": null,
      "printerTray": null,
      "rasterize": false,
      "rotation": 0,
      "scaleContent": true,
      "size": {
        "width": "4",
        "height": "6"
      },
      "units": "in",
      "forceRaw": false,
      "encoding": null,
      "spool": {}
    },
    "data": [
      {
        "type": "pixel",
        "format": "pdf",
        "flavor": "file",
        "data": "https://demo.qz.io/assets/pdf_sample.pdf",
        "options": {
          "pageWidth": "8.5",
          "pageHeight": "11",
          "pageRanges": "",
          "ignoreTransparency": false,
          "altFontRendering": false
        }
      },
      {
        "type": "pixel",
        "format": "image",
        "flavor": "file",
        "data": "https://demo.qz.io/assets/img/image_sample.png",
      },
      "^XA\n",
      "^FO50,50^ADN,36,20^FDPRINTED WITH QZ 2.2.4-SNAPSHOT\n",
      "^FS\n",
      "^XZ\n"
    ]
  },
  "signature": "",
  "timestamp": 1713895560783,
  "uid": "64t63d",
  "position": {
    "x": 720,
    "y": 462.5
  },
  "signAlgorithm": "SHA512"
}