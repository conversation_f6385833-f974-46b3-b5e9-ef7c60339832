//
// Firefox AutoConfig Certificate Installer for Legacy Firefox versions
// This is part of the QZ Tray application
//
var serviceObserver = {
    observe: function observe(aSubject, aTopic, aData) {
        // Get NSS certdb object
        var certdb = getCertDB();

        if (needsUninstall()) {
            deleteCertificate();
            unregisterProtocol();
        } else if (needsCert()) {
            deleteCertificate();
            installCertificate();
            registerProtocol();
        }

        // Compares the timestamp embedded in this script against that stored in the browser's about:config
        function needsCert() {
            try {
                return getPref("%PROPS_FILE%.installer.timestamp") != "%TIMESTAMP%";
            } catch(notfound) {}
            return true;
        }

        // Installs the embedded base64 certificate into the browser
        function installCertificate() {
            certdb.addCertFromBase64(getCertData(), "C,C,C", "%COMMON_NAME% - %ABOUT_COMPANY%");
            pref("%PROPS_FILE%.installer.timestamp", "%TIMESTAMP%");
        }

        // Deletes the certificate, if it exists
        function deleteCertificate() {
            var certs = certdb.getCerts();
            var enumerator = certs.getEnumerator();
            while (enumerator.hasMoreElements()) {
                var cert = enumerator.getNext().QueryInterface(Components.interfaces.nsIX509Cert);
                if (cert.containsEmailAddress("%ABOUT_EMAIL%")) {
                    try {
                        certdb.deleteCertificate(cert);
                    } catch (ignore) {}
                }
            }
            pref("%PROPS_FILE%.installer.timestamp", "-1");
        }

        // Register the specified protocol to open with the specified application
        function registerProtocol() {
            // Only register if platform needs it (e.g. macOS)
            var trayApp = "%APP_PATH%";
            if (!trayApp) { return; }
            try {
                var hservice = Components.classes["@mozilla.org/uriloader/handler-service;1"].getService(Components.interfaces.nsIHandlerService);
                var pservice = Components.classes["@mozilla.org/uriloader/external-protocol-service;1"].getService(Components.interfaces.nsIExternalProtocolService);

                var file = Components.classes["@mozilla.org/file/local;1"].createInstance(Components.interfaces.nsIFile);
                file.initWithPath(trayApp);

                var lhandler = Components.classes["@mozilla.org/uriloader/local-handler-app;1"].createInstance(Components.interfaces.nsILocalHandlerApp);
                lhandler.executable = file;
                lhandler.name = "%PROPS_FILE%";

                var protocol = pservice.getProtocolHandlerInfo("%DATA_DIR%");
                protocol.preferredApplicationHandler = lhandler;
                protocol.preferredAction = 2; // useHelperApp
                protocol.alwaysAskBeforeHandling = false;
                hservice.store(protocol);
            } catch(ignore) {}
        }

        // De-register the specified protocol from opening with the specified application
        function unregisterProtocol() {
            // Only register if platform needs it (e.g. macOS)
            var trayApp = "%APP_PATH%";
            if (!trayApp) { return; }
            try {
                var hservice = Components.classes["@mozilla.org/uriloader/handler-service;1"].getService(Components.interfaces.nsIHandlerService);
                var pservice = Components.classes["@mozilla.org/uriloader/external-protocol-service;1"].getService(Components.interfaces.nsIExternalProtocolService);
                hservice.remove(pservice.getProtocolHandlerInfo("%DATA_DIR%"));
            } catch(ignore) {}
        }

        // Get certdb object
        function getCertDB() {
            // Import certificate using NSS certdb API (http://tinyurl.com/x509certdb)
            var id = "@mozilla.org/security/x509certdb;1";
            var db1 = Components.classes[id].getService(Components.interfaces.nsIX509CertDB);
            var db2 = db1;
            try {
                db2 = Components.classes[id].getService(Components.interfaces.nsIX509CertDB2);
            } catch(ignore) {}
            return db2;
        }

        // The certificate to import (automatically generated by desktop installer)
        function getCertData() {
            return "%CERT_DATA%";
        }

        // Whether or not an uninstall should occur, flagged by the installer/uninstaller
        function needsUninstall() {
            try {
                if (getPref("%PROPS_FILE%.installer.timestamp") == "-1") {
                    return false;
                }
            }
            catch(notfound) {
                return false;
            }
            return %UNINSTALL%;
        }
    }
};

Components.utils.import("resource://gre/modules/Services.jsm");
Services.obs.addObserver(serviceObserver, "profile-after-change", false);