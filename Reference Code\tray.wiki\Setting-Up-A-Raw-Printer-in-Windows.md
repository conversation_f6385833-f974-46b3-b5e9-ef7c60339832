### Compatibility

* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :white_check_mark: 2.0 | :white_check_mark: 1.9 | [...](Roadmap)
*  **Windows** | [Mac](setting-up-a-raw-printer-in-osx) | [Linux](setting-up-a-raw-printer-in-ubuntu-linux)

This tutorial is for raw printers _only_. If unsure please see [What is Raw Printing?](What-is-Raw-Printing)

### Background
These steps will allow a USB, Network, Serial or Parallel attached printer to receive raw commands on Windows.  Note:  Many printer drivers do NOT need these additional steps and will work fine with both raw and pixel API calls.

> **Note:** This tutorial also applies broadly to all versions of Windows, but some steps may vary slightly.

### Steps

1. Open **Control Panel > View Devices and Printers > Add Printer.**  
   > **Note:** Also available under **Settings > Bluetooth and Devices > Printers and Scanners > Add device > Add manually**

   <img width="595" alt="image" src="https://user-images.githubusercontent.com/6345473/213093901-726b72ce-93d6-447f-8ae6-da37ff613fde.png">

1. Click "**Add a local printer**." Ignore the USB message.
   #### For Network Printer:
    * Click **Create New Port > Standard TCP/IP Port > Next.**
    * Enter the IP address and uncheck **"Query the Printer..."**

     ***DO NOT*** click "Add a Wireless, Bluetooth or Network Printer."  Always use the local port option for        Network Printers.

   #### For Serial or Parallel Printer (COM or LPT):
    * Click **Use Existing Port.**  Then, Select the appropriate COM or LPT port.

   #### For USB Printer:
    * Click **Use Existing Port.** Find the matching USB00x port that was installed with the device driver.

    * If unsure of the port, cancel the Wizard. Right Click the **USB Printer > Printer Properties > Port**. 
     (Reminder: In Windows 7, it's called "Printer Properties, not "Properties").
       > **Note:** For USB to operate properly, the device driver should already be installed. This is [automatic in  Windows 7 when connected to the internet](https://cloud.githubusercontent.com/assets/12505463/7741647/d2dd09d4-ff4f-11e4-8c7c-0851d69edbeb.png). 
       > Windows  XP may need a device driver installed manually. Download from manufacturer if needed.

   #### For Virtual Printer (FILE)
   * Click **Use Existing Port.**  Then, Select `FILE:`


1. For printer driver, select **Generic > Generic/Text Only > Next.**

    <img width="584" alt="image" src="https://user-images.githubusercontent.com/6345473/213119763-4b1c2549-0124-4871-a398-5df60b448282.png">

    > **Note:** Some print drivers, such as Zebra ZDesigner or CUPS Zebra driver have dual-mode drivers capable of accepting Raw commands as well as PostScript commands.  If Raw printing works with the vendor supplied driver, chose that over the Generic/Raw driver.

1. If prompted to replace the current driver, click **"Replace the Current Driver."**

1. **Name** the printer (ie. "zebra") **> Next**.

1. Uncheck **"Set as Default Printer"** unless instructed otherwise.

***

To learn how to print from a webpage continue to [Getting Started](getting-started)