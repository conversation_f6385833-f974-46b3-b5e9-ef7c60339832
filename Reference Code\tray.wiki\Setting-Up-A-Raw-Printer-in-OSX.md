### Compatibility

* :white_check_mark: 2.2 |:white_check_mark: 2.1 | :white_check_mark: 2.0 | :white_check_mark: 1.9 | [...](Roadmap)
* [Windows](setting-up-a-raw-printer-in-windows) | **Mac** | [Linux](setting-up-a-raw-printer-in-ubuntu-linux)

This tutorial is for raw printers _only_. If unsure please see [What is Raw Printing?](what-is-raw-printing)

### Background
These steps will allow a USB, Network attached printer to receive raw commands through Java on Apple OS X.  Note, Serial and Parallel printers are not longer supported in macOS.

***

### Steps

1. Open a Terminal window: **⌘(Option)+space > Terminal > Enter**

1. Enable CUPS web interface by entering this into the Terminal:

   ```bash
   sudo cupsctl WebInterface=yes
   ```

1. Load Safari to the CUPS web interface http://localhost:631 and click the **Administration** tab > **Add Printer**

    ![image](https://cloud.githubusercontent.com/assets/12505463/7791238/e58ca710-026a-11e5-8ec2-7dd5e7fdcd7a.png)

1. If you see your USB raw printer in the listing, ***DO NOT*** select it.
1. Click **AppSocket/HP JetDirect > Continue**

    ![image](https://cloud.githubusercontent.com/assets/12505463/7791289/a4763718-026b-11e5-9d44-63e2774b7b88.png)

1. You will be prompted for a port.
   #### For USB Printers
      * Type: (example) ``usb://Zebra/LP2844``
      * Get this port by issuing the command: 
        ``lpinfo -v |grep usb``

        > **Note:** You do not need the "?location=1a200000" information.
 
        ![image](https://cloud.githubusercontent.com/assets/12505463/7791376/38a8db06-026d-11e5-8dec-9b6753190430.png)


   #### For Network Printers
      * Type: (example) ``socket://***************:9100``
      * The IP address may be configured to use a different address, which is outside the scope of this tutorial.

1. Enter an appropriate Name, Description, and Location for your printer.

   > **Note:** If you encounter issues searching for your printer, please see [#131](https://github.com/qzind/qz-print/issues/131/)

    ![image](https://cloud.githubusercontent.com/assets/12505463/7791393/7752a8be-026d-11e5-8577-674349ba5968.png)

1. Leave the **"Share This Printer"** box unchecked and hit **Continue.**

1. Make: **Raw > Continue > Add Printer**

    ![image](https://cloud.githubusercontent.com/assets/12505463/7791423/09da169a-026e-11e5-87c3-aafbd01604f7.png)

    > **Note:** Some print drivers, such as Zebra ZDesigner or CUPS Zebra driver have dual-mode drivers capable of accepting Raw commands as well as PostScript commands.  If Raw printing works with the vendor supplied driver, choose that over the Generic/Raw driver.

1. Starting Banner: **None**

   Ending Banner: **None**

   Click **Set Default Options**

1. You may now print to your printer.  Continue to the next section **Adding a Printer Class** to make the printer appear in *System Preferences, Print & Scan*

***

### Optional - Adding a Printer Class
Adding a printer class is purely optional and allows the raw device to be listed in the System Preferences, Print & Scan area of the desktop.  Without a printer class, the printer will function properly but will not be listed in the Print & Scan area.

1. From the CUPS web interface, click **Administration > Add Class**

    ![image](https://cloud.githubusercontent.com/assets/12505463/7791451/934a7f32-026e-11e5-9b06-9105c4af423d.png)

1. Enter an appropriate Name, Description, and Location for your class. It _must_ differ from the raw printer name chosen previously.

   > **Note:** If you encounter issues searching for your printer, please see [#131](https://github.com/qzind/qz-print/issues/131/)

1. in **Members**, select the **raw printer name** chosen previously **> Add Class**

1. in **System Preferences > Print & Scan**, your new printer class will be listed.  You may use this to monitor print jobs.

    ![image](https://cloud.githubusercontent.com/assets/12505463/7791506/5b336a18-026f-11e5-8a3e-49027fb240c4.png)

***

To learn how to print from a webpage continue to:
 * [2.0 Getting Started](2.0-Getting-Started)
