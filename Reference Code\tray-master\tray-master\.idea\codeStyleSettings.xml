<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectCodeStyleSettingsManager">
    <option name="PER_PROJECT_SETTINGS">
      <value>
        <option name="RIGHT_MARGIN" value="160" />
        <option name="JD_KEEP_INVALID_TAGS" value="false" />
        <option name="JD_DO_NOT_WRAP_ONE_LINE_COMMENTS" value="true" />
        <option name="JD_KEEP_EMPTY_PARAMETER" value="false" />
        <option name="JD_KEEP_EMPTY_EXCEPTION" value="false" />
        <option name="JD_KEEP_EMPTY_RETURN" value="false" />
        <option name="HTML_SPACE_INSIDE_EMPTY_TAG" value="true" />
        <option name="HTML_KEEP_WHITESPACES_INSIDE" value="pre,textarea" />
        <option name="HTML_INLINE_ELEMENTS" value="" />
        <option name="HTML_DONT_ADD_BREAKS_IF_INLINE_CONTENT" value="title,h1,h2,h3,h4,h5,h6,p,span,br,em,strong,li,a" />
        <option name="FORMATTER_TAGS_ENABLED" value="true" />
        <CssCodeStyleSettings>
          <option name="HEX_COLOR_UPPER_CASE" value="true" />
        </CssCodeStyleSettings>
        <JSCodeStyleSettings>
          <option name="SPACE_BEFORE_FUNCTION_LEFT_PARENTH" value="false" />
          <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
        </JSCodeStyleSettings>
        <XML>
          <option name="XML_LEGACY_SETTINGS_IMPORTED" value="true" />
        </XML>
        <codeStyleSettings language="JAVA">
          <option name="LINE_COMMENT_AT_FIRST_COLUMN" value="false" />
          <option name="BLOCK_COMMENT_AT_FIRST_COLUMN" value="false" />
          <option name="KEEP_FIRST_COLUMN_COMMENT" value="false" />
          <option name="CATCH_ON_NEW_LINE" value="true" />
          <option name="FINALLY_ON_NEW_LINE" value="true" />
          <option name="ALIGN_MULTILINE_PARAMETERS_IN_CALLS" value="true" />
          <option name="ALIGN_MULTILINE_THROWS_LIST" value="true" />
          <option name="ALIGN_MULTILINE_ARRAY_INITIALIZER_EXPRESSION" value="true" />
          <option name="SPACE_AFTER_COMMA_IN_TYPE_ARGUMENTS" value="false" />
          <option name="SPACE_AFTER_TYPE_CAST" value="false" />
          <option name="SPACE_BEFORE_WHILE_PARENTHESES" value="false" />
          <option name="SPACE_BEFORE_FOR_PARENTHESES" value="false" />
          <option name="SPACE_BEFORE_TRY_PARENTHESES" value="false" />
          <option name="SPACE_BEFORE_CATCH_PARENTHESES" value="false" />
          <option name="SPACE_BEFORE_SWITCH_PARENTHESES" value="false" />
          <option name="SPACE_BEFORE_SYNCHRONIZED_PARENTHESES" value="false" />
          <option name="SPACE_BEFORE_ARRAY_INITIALIZER_LBRACE" value="true" />
          <option name="SPACE_BEFORE_QUEST" value="false" />
          <option name="SPACE_BEFORE_COLON" value="false" />
          <option name="SPACE_AFTER_COLON" value="false" />
          <option name="KEEP_SIMPLE_BLOCKS_IN_ONE_LINE" value="true" />
          <option name="KEEP_SIMPLE_METHODS_IN_ONE_LINE" value="true" />
          <option name="KEEP_SIMPLE_CLASSES_IN_ONE_LINE" value="true" />
          <option name="KEEP_MULTIPLE_EXPRESSIONS_IN_ONE_LINE" value="true" />
          <option name="IF_BRACE_FORCE" value="3" />
          <option name="DOWHILE_BRACE_FORCE" value="3" />
          <option name="WHILE_BRACE_FORCE" value="3" />
          <option name="FOR_BRACE_FORCE" value="3" />
        </codeStyleSettings>
        <codeStyleSettings language="JavaScript">
          <option name="CATCH_ON_NEW_LINE" value="true" />
          <option name="FINALLY_ON_NEW_LINE" value="true" />
          <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
          <option name="ALIGN_MULTILINE_PARAMETERS_IN_CALLS" value="true" />
          <option name="ALIGN_MULTILINE_BINARY_OPERATION" value="true" />
          <option name="SPACE_BEFORE_WHILE_PARENTHESES" value="false" />
          <option name="SPACE_BEFORE_FOR_PARENTHESES" value="false" />
          <option name="SPACE_BEFORE_CATCH_PARENTHESES" value="false" />
          <option name="SPACE_BEFORE_SWITCH_PARENTHESES" value="false" />
          <option name="KEEP_SIMPLE_BLOCKS_IN_ONE_LINE" value="true" />
          <option name="KEEP_SIMPLE_METHODS_IN_ONE_LINE" value="true" />
          <option name="IF_BRACE_FORCE" value="3" />
          <option name="DOWHILE_BRACE_FORCE" value="3" />
          <option name="WHILE_BRACE_FORCE" value="3" />
          <option name="FOR_BRACE_FORCE" value="3" />
        </codeStyleSettings>
      </value>
    </option>
    <option name="USE_PER_PROJECT_SETTINGS" value="true" />
  </component>
</project>