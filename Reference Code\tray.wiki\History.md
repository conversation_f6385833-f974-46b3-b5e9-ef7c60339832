# The QZ Story

## The Java Applet

In 2008, <PERSON><PERSON> wrote "[jZebra](https://code.google.com/archive/p/jzebra)", a free, open source [Java Applet](https://wikipedia.org/wiki/Java_applet) to circumvent limitations imposed by the web browser when printing to industrial hardware.  Although initially written for magnetic card printing, the applet quickly grew in popularity for wider uses such as shipping labels (e.g. [ZPL](https://wikipedia.org/wiki/Zebra_Programming_Language), [EPL](https://wikipedia.org/wiki/Eltron_Programming_Language)) and eventually receipt printers (e.g. [ESC/POS](https://en.wikipedia.org/wiki/ESC/P)) -- which too -- required special considerations to handle low-level commands to this hardware.

Over its 5-year lifespan, the jZebra project answered over 3,000 emails -- more than 2 emails per day.

## Starting A Company

In 2013, Oracle (e.g. Java) [began requiring certificates for Java Applets](https://www.oracle.com/java/technologies/javase/web-start-code-signing.html), resulting in an influx of security warnings to jZebra users -- in some cases -- inhibiting the ability to print altogether.

Faced with costs of certification combined with community demand, QZ Industries LLC was born as a partnership between programmer <PERSON><PERSON> and brother Lite Finocchiaro.

That year, jZebra was rebranded as "[qz-print](https://github.com/qzind/qz-print/tree/1.8)", and for paying customers, was offered with a trusted certificate, compliant with Oracle's new security requirements.

## Java's Dead.  Long Live Java.

### The wildcard problem

In 2014, Oracle [further restricted the abilities of a Java Applet](https://stackoverflow.com/a/23262784/3196753), placing hard restrictions on which websites can run Java code.  QZ fulfilled this need by hosting a custom build of qz-print for each customer, an unsustainable effort.

### The NPAPI problem

Later that year -- to further thwart security concerns -- browsers started [blocking Java altogether](https://stackoverflow.com/q/27057816/3196753), forcing qz-print to stop using the Java Applet technology.  Applets would soon stop working in all browsers.

Early 2015, qz-print introduced a websocket wrapper, allowing the applet code to continue functioning as a standalone desktop application.

## QZ Tray is born

On June 1st, 2015 "QZ Tray" -- qz-print, but running in the system tray -- was a websocket-first rewrite but with a new asynchronous-by-design strategy using JavaScript Promises.  In collaboration with [3rdsoft](https://3rdsoft.com/) and from the core of qz-print, a new API and desktop application were born.

## Work smarter, not harder

In 2016, HID device support was added, adding a wide range of hardware support, such as barcode scanners, weight scales and much more.

In 2020, the desktop installer was rewritten using pure Java, removing development redundancies empower custom deployment tasks.

Following its success, QZ now works directly with upstream projects such as OpenJDK, OpenJFX, PDFBOX and JNA (and others) to solidify its role in the Java ecosystem.

By partnering on features, fixes and detailed user feedback, QZ now focuses its energy on leading the industry on improved quality, performance and features with each new release, while maintaining seemless backwards compatiblity.