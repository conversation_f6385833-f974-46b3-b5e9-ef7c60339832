### Compatibility

* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :no_entry: 2.0 | :no_entry: 1.9 | [...](Roadmap)

### Objective

* Retrieve status information from printers (.e.g. out of paper, offline, etc.).

### Outline

* [Listen for printer status](#listen-for-printer-status)
* [Setup Callback](#setup-callback)
* [Get Status](#get-status)
* [Sample Event](#sample-event)
* [Full Example](#full-example)
* [Printer and Job Statuses](#printer-and-job-statuses)
* [Advanced](#advanced)
   * [Printer Contents Monitoring](#printer-contents-monitoring)

### Listen for Printer Status
Select the printers to get statuses on using [`qz.printers.startListening(...)`](https://qz.io/api/qz.printers#.startListening)

```js
// Specific printer
qz.printers.startListening("ZDesigner LP2844")
   .catch(err => console.error(err);

// All printers
qz.printers.startListening()
   .catch(err => console.error(err)

// Based on best-match
qz.printers.find("ZDesigner").then(printer => {
   console.log("Listening for printer events", printer));
   return qz.printers.startListening(printerName);
}).catch(err => console.error(err));
```

### Setup Callback

Setup a callback to fire when status changes and request status using [`qz.printers.setPrinterCallbacks(...)`](https://qz.io/api/qz.printers#.setPrinterCallbacks) and request immediate status using [`qz.printers.getStatus()`](https://qz.io/api/qz.printers#.getStatus).

```js
qz.printers.setPrinterCallbacks(evt => console.log(evt.severity, evt.eventType, evt.message));

qz.printers.getStatus().then(() => console.log("Listening on printer status"));
   .catch(err => console.error(err));
```

### Sample Event
```js
{
   printerName: "PDFwriter",
   jobName: "My Sample PDF",                                  /* since 2.1.3 */
   eventType: "JOB",                                          /* since 2.1.3 */
   statusText: "PAUSED",                                      /* since 2.1.3 */
   severity: "WARN",
   statusCode: "media-empty", // (or for Windows, 0x00000040) /* since 2.1.3 */
   message: ...
}
```

### Get Status
Gets the most recent status of all printers that are currently being listened to using [`qz.printers.getStatus(...)`](https://qz.io/api/qz.printers#.getStatus).  Assumes a listener has already been added.  This is helpful for when a status arrived prior to listening, the most recently stored status should be delivered.

```js
// Request the current status of all printers
qz.printers.getStatus().then(() => console.log("Requesting all printer statuses for listened printers"))
   .catch(err => console.error(err));
```

### Stop listening

Stop listening to all printers

```js
qz.printers.stopListening().then(() => console.log("Stopped listening"))
   .catch(err => console.error(err));
```

### Printer and Job Statuses

Job Statuses and Printer Statuses are captured by CUPS or Winspool and mapped to generic statuses.  See also: [`src/qz/printer/status/job`](https://github.com/qzind/tray/tree/master/src/qz/printer/status/job), [`src/qz/printer/status/printer`](https://github.com/qzind/tray/tree/master/src/qz/printer/status/printer).
* `eventType: "JOB"`

    | `severity` | `statusText` |
    |----------|------------|
    | ❌ `ERROR` | `ABORTED`|
    | ❌ `ERROR` | `ERROR` |
    | ❌ `ERROR` | `OFFLINE` |
    | ⚠️ `WARN` | `CANCELED` |
    | ⚠️ `WARN` | `PAPEROUT` |
    | ⚠️ `WARN` | `RESTART` |
    | ⚠️ `WARN` | `USER_INTERVENTION` |
    | ✅ `INFO` | `COMPLETE` |
    | ✅ `INFO` | `DELETED` |
    | ✅ `INFO` | `DELETING` |
    | ✅ `INFO` | `PRINTING` |
    | ✅ `INFO` | `SPOOLING` |
    | ✅ `INFO` | `SCHEDULED` |
    | ✅ `INFO` | `RETAINED` |
    | ✅ `INFO` | `PAUSED` |
    | ✅ `INFO` | `SENT` |
    | ✅ `INFO` | `RENDERING_LOCALLY` |
    | ✅ `INFO` | `UNKNOWN` |
    | 💀 `FATAL` | `UNMAPPED` |

* `eventType: "PRINTER"`

    | `severity` | `statusText` |
    |----------|------------|
    | 💀 `FATAL` | `ERROR` |
    | 💀 `FATAL` | `PAPER_JAM` |
    | 💀 `FATAL` | `OFFLINE` |
    | 💀 `FATAL` | `NOT_AVAILABLE` |
    | 💀 `FATAL` | `NO_TONER` |
    | 💀 `FATAL` | `PAGE_PUNT` |
    | 💀 `FATAL` | `OUT_OF_MEMORY` |
    | 💀 `FATAL` | `UNMAPPED` |
    | ⚠️ `WARN` | `PAUSED` |
    | ⚠️ `WARN` | `PAPER_OUT` |
    | ⚠️ `WARN` | `PAPER_PROBLEM` |
    | ⚠️ `WARN` | `PENDING_DELETION` |
    | ⚠️ `WARN` | `OUTPUT_BIN_FULL` |
    | ⚠️ `WARN` | `TONER_LOW` |
    | ⚠️ `WARN` | `USER_INTERVENTION` |
    | ⚠️ `WARN` | `DOOR_OPEN` |
    | ⚠️ `WARN` | `SERVER_UNKNOWN` |
    | ✅ `INFO` | `OK` |
    | ✅ `INFO` | `MANUAL_FEED` |
    | ✅ `INFO` | `IO_ACTIVE` |
    | ✅ `INFO` | `BUSY` |
    | ✅ `INFO` | `PRINTING` |
    | ✅ `INFO` | `WAITING` |
    | ✅ `INFO` | `PROCESSING` |
    | ✅ `INFO` | `INITIALIZING` |
    | ✅ `INFO` | `WARMING_UP` |
    | ✅ `INFO` | `POWER_SAVE` |
    | ✅ `INFO` | `UNKNOWN` |


### Full example

```js
//setup a callback
qz.printers.setPrinterCallbacks((evt) => { console.log(evt.severity, evt.eventType, evt.message); });

function getPrintersStatus () {
    // get the status of a specific printer
    qz.printers.find("Printer Name").then(printer => {
        // listen to the printer
        qz.printers.startListening(printer).then(() => {
            return qz.printers.getStatus();
        });
    }).catch(function(e) { console.error(e); });
};
```


![image](https://user-images.githubusercontent.com/12505463/54481694-5f78b900-480e-11e9-8b1f-400b2601aba0.png)

### Advanced

#### Printer Contents Monitoring

Since 2.2.2, QZ Tray has the ability to [receive raw job data](https://github.com/qzind/tray/issues/923) (e.g. `.PRN` file), but has the following requirements:
* This feature is Windows only (this feature is not yet supported on macOS, Linux)
* User must be able to [read the spool file location](https://superuser.com/q/1740212/443147)
* `qz-tray.properties` must be modified to toggle this feature on or you will receive the error `Job data listeners are currently disabled`.

   ```properties
   #Wed Dec 14 16:16:09 EST 2022
   printer.status.jobdata=true
   ```
* An additional parameter `jobData` must be provided to [startListening(...)](https://qz.io/api/qz.printers#.startListening).


   ```js
   var options = {
       jobData: true,      // toggle job data
   };

   qz.printers.startListening(null /* all printers */, options);
   ```

* The format and size of the data can be manipulated using [`flavor`](https://qz.io/api/qz.printers#.startListening) and [`maxJobData`](https://qz.io/api/qz.printers#.startListening), respectively.

   ```js
   var options = {
       jobData: true,      // toggle job data
       flavor: 'base64',   // return spool file content as 'base64' (default is 'plain')
       maxJobData: 64      // only return data less than or equal to 64 bytes (default is -1)
   };

   qz.printers.startListening(null /* all printers */, options);
   ```
   