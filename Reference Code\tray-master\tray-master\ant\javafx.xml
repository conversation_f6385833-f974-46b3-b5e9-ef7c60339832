<project name="javafx" default="download-javafx" basedir="..">
    <property file="ant/project.properties"/>
    <import file="${basedir}/ant/platform-detect.xml"/>
    <import file="${basedir}/ant/version.xml"/>

    <!-- TODO: Short-circuit download if host and target are identical? -->
    <target name="download-javafx" depends="download-javafx-host,download-javafx-target"/>

    <target name="download-javafx-host" unless="${host.fx.exists}" depends="get-javafx-versions,host-fx-exists">
        <antcall target="download-extract-javafx">
            <param name="fx.os" value="${host.os}"/>
            <param name="fx.arch" value="${host.arch}"/>
            <param name="fx.id" value="${host.fx.id}"/>
            <param name="fx.basedir" value="${host.fx.basedir}"/>
            <param name="fx.dir" value="${host.fx.dir}"/>
            <param name="fx.ver" value="${host.fx.ver}"/>
            <param name="fx.majver" value="${host.fx.majver}"/>
            <param name="fx.urlver" value="${host.fx.urlver}"/>
        </antcall>
    </target>

    <target name="download-javafx-target" unless="${target.fx.exists}" depends="get-javafx-versions,target-fx-exists">
        <antcall target="download-extract-javafx">
            <param name="fx.os" value="${target.os}"/>
            <param name="fx.arch" value="${target.arch}"/>
            <param name="fx.id" value="${target.fx.id}"/>
            <param name="fx.basedir" value="${target.fx.basedir}"/>
            <param name="fx.dir" value="${target.fx.dir}"/>
            <param name="fx.majver" value="${target.fx.majver}"/>
            <param name="fx.urlver" value="${target.fx.urlver}"/>
        </antcall>
    </target>

    <target name="host-fx-exists" depends="platform-detect">
        <!-- Host fx is saved to lib/ -->
        <property name="host.fx.basedir" value="${basedir}/lib"/>
        <property name="host.fx.id" value="javafx-${host.os}-${host.arch}-${host.fx.urlver}"/>
        <property name="host.fx.dir" value="${host.fx.basedir}/${host.fx.id}"/>
        <mkdir dir="${host.fx.dir}"/>

        <!-- File to look for: "glass.dll", "libglass.dylib" or "libglass.so" -->
        <property name="host.libglass" value="${host.libprefix}glass.${host.libext}"/>

        <!-- Grab the first file match -->
        <first id="host.fx.files">
            <fileset dir="${host.fx.dir}">
                <include name="**/${host.libglass}"/>
            </fileset>
        </first>
        <!-- Convert the file to a usable string -->
        <pathconvert property="host.fx.path" refid="host.fx.files"/>

        <!-- Set our flag if found -->
        <condition property="host.fx.exists">
            <not><equals arg1="${host.fx.path}" arg2=""/></not>
        </condition>

        <!-- Human readable message -->
        <condition property="host.fx.message"
                   value="JavaFX host platform file ${host.libglass} found, skipping download.${line.separator}Location: ${host.fx.path}"
                   else="JavaFX host platform file ${host.libglass} is missing, will download.${line.separator}Searched: ${host.fx.dir}">
            <isset property="host.fx.exists"/>
        </condition>

        <echo level="info">${host.fx.message}</echo>
    </target>

    <target name="target-fx-exists">
        <!-- Target fx is saved to out/ -->
        <property name="target.fx.basedir" value="${out.dir}"/>
        <property name="target.fx.id" value="javafx-${target.os}-${target.arch}-${target.fx.urlver}"/>
        <property name="target.fx.dir" value="${target.fx.basedir}/${target.fx.id}"/>
        <mkdir dir="${target.fx.dir}"/>

        <!-- File to look for: "glass.dll", "libglass.dylib" or "libglass.so" -->
        <property name="target.libglass" value="${target.libprefix}glass.${target.libext}"/>

        <!-- Grab the first file match -->
        <first id="target.fx.files">
            <fileset dir="${target.fx.dir}">
                <!-- look for "glass.dll", "libglass.dylib" or "libglass.so" -->
                <include name="**/${target.libglass}"/>
            </fileset>
        </first>
        <!-- Convert the file to a usable string -->
        <pathconvert property="target.fx.path" refid="target.fx.files"/>

        <!-- Set our flag if found -->
        <condition property="target.fx.exists">
            <not><equals arg1="${target.fx.path}" arg2=""/></not>
        </condition>

        <!-- Human readable message -->
        <condition property="target.fx.message"
                   value="JavaFX target platform file ${target.libglass} found, skipping download.${line.separator}Location: ${target.fx.path}"
                   else="JavaFX target platform file ${target.libglass} is missing, will download.${line.separator}Searched: ${target.fx.dir}">
            <isset property="target.fx.exists"/>
        </condition>

        <echo level="info">${target.fx.message}</echo>
    </target>

    <!--
        Populates: host.fx.ver, host.fx.urlver, target.fx.ver, target.fx.urlver

        - Converts version to a usable URL format
        - Leverage older releases for Intel builds until upstream bug report SUPQZ-14 is fixed

        To build:   We need javafx to download a javafx which matches "host.os" and "host.arch"
        To package: We need javafx to download a javafx which matches "target.os" and "target.arch"
     -->
    <target name="get-javafx-versions" depends="platform-detect">
        <!-- Fallback to sane values -->
        <property name="host.fx.ver" value="${javafx.version}"/>
        <property name="target.fx.ver" value="${javafx.version}"/>

        <!-- Handle pesky url "." = "-" differences -->
        <loadresource property="host.fx.urlver">
            <propertyresource name="host.fx.ver"/>
            <filterchain>
                <tokenfilter>
                    <filetokenizer/>
                    <replacestring from="." to="-"/>
                </tokenfilter>
            </filterchain>
        </loadresource>
        <loadresource property="target.fx.urlver">
            <propertyresource name="target.fx.ver"/>
            <filterchain>
                <tokenfilter>
                    <filetokenizer/>
                    <replacestring from="." to="-"/>
                </tokenfilter>
            </filterchain>
        </loadresource>
        <property description="suppress property warning" name="target.fx.urlver" value="something went wrong"/>
        <property description="suppress property warning" name="host.fx.urlver" value="something went wrong"/>

        <!-- Calculate our javafx "major" version -->
        <loadresource property="host.fx.majver">
            <propertyresource name="host.fx.ver"/>
            <filterchain>
                <replaceregex pattern="[-_.].*" replace="" />
            </filterchain>
        </loadresource>
        <loadresource property="target.fx.majver">
            <propertyresource name="target.fx.ver"/>
            <filterchain>
                <replaceregex pattern="[-_.].*" replace="" />
            </filterchain>
        </loadresource>
        <property description="suppress property warning" name="target.fx.majver" value="something went wrong"/>
        <property description="suppress property warning" name="host.fx.majver" value="something went wrong"/>

        <echo level="info">
        JavaFX host platform:
            Version: ${host.fx.ver} (${host.os}, ${host.arch})
            Major Version: ${host.fx.majver}
            URLs: &quot;${host.fx.urlver}&quot;

        JavaFX target platform:
            Version: ${target.fx.ver} (${target.os}, ${target.arch})
            Major Version: ${target.fx.majver}
            URLs: "&quot;${target.fx.urlver}&quot;
        </echo>
    </target>

    <!-- Downloads and extracts javafx for the specified platform -->
    <target name="download-extract-javafx">
        <!-- Cleanup old versions -->
        <delete includeemptydirs="true" defaultexcludes="false">
            <fileset dir="${fx.basedir}">
                <include name="javafx*/"/>
            </fileset>
        </delete>
        <mkdir dir="${fx.dir}"/>

        <!-- Valid os values: "windows", "linux", "osx" -->
        <!-- translate "mac" to "osx" -->
        <condition property="fx.os.fixed" value="osx" else="${fx.os}">
            <equals arg1="${fx.os}" arg2="mac"/>
        </condition>

        <!-- Valid arch values: "x64", "aarch64", "x86" -->
        <!-- translate "x86_64" to "x64" -->
        <condition property="fx.arch.fixed" value="x64">
            <or>
                <equals arg1="${fx.arch}" arg2="x86_64"/>
                <and>
                    <!-- TODO: Remove "aarch64" to "x64" when windows aarch64 binaries become available -->
                    <equals arg1="${fx.arch}" arg2="aarch64"/>
                    <equals arg1="${fx.os}" arg2="windows"/>
                </and>
                <and>
                    <!-- TODO: Remove "riscv" to "x64" when linux riscv64 binaries become available -->
                    <equals arg1="${fx.arch}" arg2="riscv64"/>
                    <equals arg1="${fx.os}" arg2="linux"/>
                </and>
            </or>
        </condition>
        <property name="fx.arch.fixed" value="${fx.arch}" description="fallback value"/>

        <!-- Fix underscore when "monocle" is missing -->
        <condition property="fx.url" value="${javafx.mirror}/${fx.majver}/openjfx-${fx.urlver}_${fx.os.fixed}-${fx.arch.fixed}_bin-sdk.zip">
            <not>
                <contains string="${fx.urlver}" substring="monocle"/>
            </not>
        </condition>

        <property name="fx.url" value="${javafx.mirror}/${fx.majver}/openjfx-${fx.urlver}-${fx.os.fixed}-${fx.arch.fixed}_bin-sdk.zip"/>
        <property name="fx.zip" value="${out.dir}/${fx.id}.zip"/>

        <echo level="info">Downloading JavaFX from ${fx.url}</echo>
        <echo level="info">Temporarily saving JavaFX to ${fx.zip}</echo>

        <mkdir dir="${out.dir}"/>
        <get src="${fx.url}" verbose="true" dest="${fx.zip}"/>
        <unzip src="${fx.zip}" dest="${fx.dir}" overwrite="true"/>
        <delete file="${fx.zip}"/>
    </target>
</project>
