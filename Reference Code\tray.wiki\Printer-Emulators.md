### Compatibility

* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :white_check_mark: 2.0 | :white_check_mark: 1.9 | [...](Roadmap)

### Background

The high costs of printer hardware can make a developer's job difficult when designing a product which has printing capabilities.  Various methods exist for printing to virtual printers which avoid the need to purchase physical hardware during the research and development phases of a project as well as baseline troubleshooting of functionality.

Printer emulation is a critical part of reducing the overhead associated with a development lifecycle.  Listed below are common printer hardware emulators/methods.

### Pixel

| OS          | Name                         | [Pixel](2.0-Pixel-Printing) | Comments                           |
|-------------|------------------------------|-------|--------------------------------------------------------|
| Windows     |PDFCreator                    |✔️    | Recommended choice for Windows. [Download](http://www.pdfforge.org/pdfcreator)|
| Windows     |CutePDF                       |✔️    | Good alternative to PDFCreator                          |
| Windows     |PrimoPDF                      |✔️    | Good alternative to PDFCreator                          |
| Windows     |Microsoft XPS Document Writer |✔️    | 600 dpi only; known to degrade image quality            |
| Windows     |Microsoft Print to PDF        |✔️    | 600 dpi only; known to degrade image quality           |
| macOS       |RWTS PDFwriter                |✔️    | Recommended choice for MacOS. [Download](https://github.com/rodyager/RWTS-PDFwriter/releases)|
| macOS       |Lisanet PDFwriter             |✔️    | Requires workarounds for macOS 10.10+                  |
| Linux       |CUPS-PDF                      |✔️    | Recommended choice for Linux.  Download via [`apt`](https://launchpad.net/cups-pdf)/[`dnf`](https://apps.fedoraproject.org/packages/cups-pdf)/etc |


### Raw
| OS      | Name                         | [Raw](raw) | Comments                               |
|---------|------------------------------|---------|--------------------------------------------------------|
| All     | esc2html                     | ✔️ ESC/POS | See [escpos-tools GitHub project](https://github.com/receipt-print-hq/escpos-tools) |
| Windows |QPCPrint                 | ✔️ ESC/P2 | Windows 7 or lower, disable UAC. [Download](https://www.kilgus.net/qpcprint/)     |
| Windows |Bematech                 | ✔️ ESC/Bema | [Download](http://partners.bematech.com.br/suporte-e-recursos/paginas/DetalhesDownload.aspx?ID=100) (Portugese language) |
| Windows |`FILE:`                  | ✔️ Raw | [Configure](Setting-Up-A-Raw-Printer-in-Windows#for-virtual-printer-file), Inspect output using [XVI32](http://www.chmaas.handshake.de/delphi/freeware/xvi32/xvi32.htm) hex editor |
| Windows | PSA Output Manager | ✔️ IGP/PGL (Printronix Graphics Language) | Recommended for IGP/PGL/Printronix testing.  Please inquire with [TSC support](https://usca.tscprinters.com/en/technical-support) for a link to this software. |
| Windows | Virtual ZPL Printer | ✔️ ZPL | Recommended for ZPL.  [Visit site](https://github.com/porrey/Virtual-ZPL-Printer) |
| All     |EPLPrinter               | ✔️ EPL | Recommended for copy/pasting EPL.  [Visit site](https://eplprinter.azurewebsites.net/) |
| All     |Labelary                 | ✔️ ZPL | Recommended for copy/pasting ZPL.  [Visit site](http://labelary.com/viewer.html) |
| All     |Zpl Printer (Emulator, Chrome app)| ✔️ ZPL | Requires Chromium 108 or older. [Download](https://chrome.google.com/webstore/detail/zpl-printer/phoidlklenidapnijkabnfdgmadlcmjo/related?hl=en-US) <br/>**Notes:** <ol><li>Install the app from the Chrome Webstore</li><li>Find the app by searching Start/Spotlight</li><li>Edit the settings and increase the buffer to something large (e.g. 50000B).</li><li>Edit the settings to set the paper size</li><li>When printing using QZ you may use ["set to host"](https://user-images.githubusercontent.com/12505463/36881652-35f76546-1d9d-11e8-85af-2e5dd3720c14.png) to print.  Alternately, you may add a local raw printer which points to `127.0.0.1` with the emulator also listening on `127.0.0.1`.</li><li>Last, and this is important, make sure to toggle the emulator back "On" after making changes.</li></ol> |




