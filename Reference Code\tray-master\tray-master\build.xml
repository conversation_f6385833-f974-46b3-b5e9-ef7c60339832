<?xml version="1.0" encoding="UTF-8"?>

<project name="qz" default="distribute" basedir=".">
    <property file="ant/project.properties"/>
    <import file="ant/platform-detect.xml"/>
    <import file="ant/javafx.xml"/>
    <import file="ant/signing.xml"/>
    <import file="ant/apple/installer.xml"/>
    <import file="ant/linux/installer.xml"/>
    <import file="ant/windows/installer.xml"/>

    <!-- Fixes some odd empty directory issues -->
    <defaultexcludes remove="**/.DS_Store"/>

    <target name="distribute" depends="init,quick-clean,build-jar,override-authcert,build-demo,whitelist-certs">
        <echo level="info">Process complete</echo>
    </target>

    <target name="init">
        <echo message="Building ${project.filename} using JDK ${ant.java.version}"/>
    </target>

    <target name="clean" depends="init">
        <delete dir="${out.dir}" includeemptydirs="true" defaultexcludes="false"/>
    </target>

    <target name="quick-clean">
        <!-- Cleanup lingering files (for other OS installers) -->
        <delete includeemptydirs="true" defaultexcludes="false" failonerror="false">
            <fileset dir="${out.dir}">
                <include name="**/**"/>
                <exclude name="**/jlink/jdk-**/**"/>
                <exclude name="**/javafx-**/**"/>
                <exclude name="**/dist/provision/**"/>
            </fileset>
        </delete>
    </target>

    <target name="compile-socket" depends="init">
        <mkdir dir="${build.dir}/${project.filename}"/>

        <!-- find the pdfbox jar -->
        <path id="pdfbox.found">
            <first>
                <fileset dir="lib/printing/">
                    <include name="pdfbox*.jar"/>
                </fileset>
            </first>
        </path>
        <pathconvert property="pdfbox.path" refid="pdfbox.found"/>

        <javac destdir="${build.dir}/${project.filename}" source="${javac.source}" target="${javac.target}" includeantruntime="false" encoding="UTF-8" debug="true">
            <src path="${src.dir}"/>
            <classpath>
                <!-- prefer bouncycastle from pdfbox over others -->
                <path id="plugin.override">
                    <pathelement path="${pdfbox.path}"/>
                </path>
                <fileset dir="lib">
                    <include name="**/*.jar"/>
                </fileset>
                <fileset dir="${java.home}">
                    <include name="**/*.jar"/>
                </fileset>
            </classpath>
            <compilerarg value="-Xlint:-options"/>
        </javac>

        <!-- Include non-class files from src in build directory -->
        <copy todir="${build.dir}/${project.filename}">
            <fileset dir="${src.dir}" excludes="**/*.java"/>
        </copy>

        <copy todir="${dist.dir}">
            <fileset file="LICENSE.txt"/>
        </copy>
    </target>

    <target name="build-jar" depends="download-javafx,compile-socket,copy-libs,externalize-libs">
        <echo level="info">Building jar</echo>

        <!-- Strip jlink-incompat files -->
        <echo level="info">Stripping jlink-incompatible files</echo>
        <jar compress="${jar.compress}" index="${jar.index}" destfile="${dist.dir}/${project.filename}.jar" duplicate="preserve">
            <fileset dir="${build.dir}/${project.filename}"/>
            <!-- Some root level files will cause jlink to fail, they need to be cleaned up -->
            <fileset dir="${out.dir}/libs-temp" excludes="*.class,LICENSE,jetty-dir.css,about.html,Log4j-*"/>
            <manifest>
                <attribute name="Application-Name" value="${project.name}"/>
                <attribute name="Main-Class" value="qz.App"/>
                <attribute name="Permissions" value="all-permissions"/>
                <attribute name="Multi-Release" value="true"/>
            </manifest>
        </jar>
        <delete dir="${out.dir}/libs-temp" includeemptydirs="true" defaultexcludes="false"/>

        <antcall target="sign-jar">
            <param name="sign.file" value="${dist.dir}/${project.filename}.jar"/>
        </antcall>
    </target>

    <target name="externalize-libs" if="separate.static.libs" depends="copy-dylibs,copy-dlls,copy-solibs">
        <!-- Strip embedded, native resources -->
        <delete>
            <fileset dir="${out.dir}/libs-temp">
                <include name="**/*.jnilib"/>
                <include name="**/*.dylib"/>
                <include name="**/*.dll"/>
                <include name="**/*.so"/>
                <include name="**/*.a"/>
            </fileset>
        </delete>

        <!-- Delete empty files-->
        <delete includeemptydirs="true" defaultexcludes="false">
            <fileset dir="${out.dir}/libs-temp">
                <and>
                    <size value="0"/>
                    <type type="dir"/>
                </and>
            </fileset>
        </delete>
    </target>

    <target name="copy-libs" depends="download-javafx">
        <!-- Copy jfx libs-->
        <mkdir dir="${dist.dir}/libs"/>
        <copy todir="${dist.dir}/libs" flatten="true">
            <fileset dir="${target.fx.dir}">
                <include name="**/*.${target.libext}"/>
            </fileset>
        </copy>

        <!-- Copy platform independent third-party libs -->
        <mkdir dir="${out.dir}/libs-temp"/>
        <unzip dest="${out.dir}/libs-temp" overwrite="false">
            <fileset dir="${basedir}/lib">
                <include name="**/*.jar"/>
                <exclude name="**/javafx*"/>
            </fileset>
        </unzip>
        <!-- Copy platform specific target javafx version -->
        <echo level="info">Copying ${target.fx.dir}</echo>
        <unzip dest="${out.dir}/libs-temp" overwrite="false">
            <fileset dir="${target.fx.dir}">
                <include name="**/*.jar"/>
            </fileset>
        </unzip>
        <!-- Remove files that can/did collide during unpacking -->
        <delete includeemptydirs="true" failonerror="false">
            <fileset dir="${out.dir}/libs-temp">
                <include name="LICENSE*"/>
                <include name="README*"/>
                <include name="**/META-INF/**"/>
                <!-- Used by SecurityInfo.getMavenVersions() -->
                <exclude name="**/META-INF/maven/**"/>
                <!-- Required by log4j -->
                <exclude name="**/META-INF/**/apache/logging/**"/>
                <exclude name="**/META-INF/services/**"/>
            </fileset>
        </delete>

        <!-- Merge META-INF/services from TCVN-3, icu4j-charset-slim -->
        <!-- find the icu4j-charset-slim jar -->
        <path id="icu4j-slim.found">
            <first>
                <fileset dir="lib/charsets/">
                    <include name="icu4j-charset-*.jar"/>
                </fileset>
            </first>
        </path>
        <pathconvert property="icu4j-slim.path" refid="icu4j-slim.found"/>

        <!-- find the TCVN-3 jar -->
        <path id="tcvn-3.found">
            <first>
                <fileset dir="lib/charsets/">
                    <include name="TCVN-3*.jar"/>
                </fileset>
            </first>
        </path>
        <pathconvert property="tcvn-3.path" refid="tcvn-3.found"/>

        <!-- merge service entries for CharsetProvider -->
        <concat destfile="${out.dir}/libs-temp/META-INF/services/java.nio.charset.spi.CharsetProvider" fixlastline="true">
            <zipentry zipfile="${icu4j-slim.path}" name="META-INF/services/java.nio.charset.spi.CharsetProvider"/>
            <zipentry zipfile="${tcvn-3.path}" name="META-INF/services/java.nio.charset.spi.CharsetProvider"/>
        </concat>

        <!-- Merge META-INF/services from PDFBOX, jpeg2000 and TwelveMonkeys -->
        <!-- find the TwelveMonkeys jar -->
        <path id="imageio-jpeg.found">
            <first>
                <fileset dir="lib/imaging/">
                    <include name="imageio-jpeg*.jar"/>
                </fileset>
            </first>
        </path>
        <pathconvert property="imageio-jpeg.path" refid="imageio-jpeg.found"/>

        <!-- find the jpeg2000 jar -->
        <path id="imageio-jpeg2000.found">
            <first>
                <fileset dir="lib/imaging/">
                    <include name="jai-imageio-jpeg2000*.jar"/>
                </fileset>
            </first>
        </path>
        <pathconvert property="imageio-jpeg2000.path" refid="imageio-jpeg2000.found"/>

        <!-- merge service entries for ImageReaderSpi -->
        <concat destfile="${out.dir}/libs-temp/META-INF/services/javax.imageio.spi.ImageReaderSpi" fixlastline="true">
            <zipentry zipfile="${pdfbox.path}" name="META-INF/services/javax.imageio.spi.ImageReaderSpi"/>
            <zipentry zipfile="${imageio-jpeg.path}" name="META-INF/services/javax.imageio.spi.ImageReaderSpi"/>
            <zipentry zipfile="${imageio-jpeg2000.path}" name="META-INF/services/javax.imageio.spi.ImageReaderSpi"/>
        </concat>
    </target>

    <!-- install override.crt for "community" branded builds -->
    <target name="override-authcert" if="authcert.use">
        <echo level="info">Bundling with manual cert for signing auth: ${authcert.use}</echo>
        <!-- See also: Constants.OVERRIDE_CERT -->
        <property description="suppress-property-warning" name="authcert.use" value="override.crt"/>
        <copy file="${authcert.use}" tofile="${dist.dir}/override.crt" overwrite="true"/>
    </target>

    <!-- install certs to "whitelist" directory for whitelabel builds -->
    <target name="whitelist-certs" depends="build-demo" if="whitelist.use">
        <echo level="info">Copying certificate(s) to dist/whitelist: ${whitelist.use}</echo>
        <!-- See also: Constants.WHITELIST_CERT_DIR -->

        <mkdir dir="${dist.dir}/whitelist"/>
        <property description="suppress property warning" name="whitelist.use" value=""/>
        <copy file="${whitelist.use}" todir="${dist.dir}/whitelist" overwrite="true"/>
    </target>

    <target name="build-demo" depends="init" unless="dist.minimal">
        <property description="suppress-property-warning" name="demo.dir" value="${dist.dir}/demo"/>
        <echo level="info">Copying demo resource files to ${demo.dir}</echo>

        <!-- Create the demo folder -->
        <delete dir="${demo.dir}" failonerror="false"/>
        <copy todir="${demo.dir}">
            <fileset file="sample.html"/>
            <fileset dir="${basedir}" includes="css/**"/>
            <fileset dir="${basedir}" includes="fonts/**"/>
            <fileset dir="${basedir}" includes="js/**/*.js"/>
            <fileset dir="${basedir}" includes="assets/**">
                <exclude name="**/branding/"/>
            </fileset>
        </copy>

        <!-- Handle sample.html renaming -->
        <property description="suppress-property-warning" name="sample.name" value="sample.html"/>
        <move file="${demo.dir}/sample.html" tofile="${demo.dir}/${sample.name}"/>
    </target>

    <target description="This task is deprecated" name="include-assets" depends="build-demo">
        <echo level="warn">The "include-assets" task is deprecated, please use "build-demo" instead.</echo>
    </target>

    <target name="check-provision" if="provision.file">
        <property description="suppress property warning" name="provision.file" value=""/>
        <available file="${provision.file}" property="provision.file.exists"/>
        <fail message="Provision file was provided but not found: '${provision.file}'" unless="provision.file.exists"/>
        <echo level="info">Found provision file '${provision.file}' calling 'provision --json [...]'</echo>
    </target>

    <target name="clean-provision" depends="check-provision" unless="skip.clean.provision">
        <delete dir="${provision.dir}" includeemptydirs="true" defaultexcludes="false"/>
    </target>

    <target name="provision" depends="check-provision,clean-provision" if="provision.file.exists">
        <java jar="${dist.dir}/${project.filename}.jar" fork="true" failonerror="true">
            <arg value="provision"/>
            <arg value="--json"/>
            <arg value="${provision.file}"/>
            <arg value="--target-os"/>
            <arg value="${target.os}"/>
            <arg value="--target-arch"/>
            <arg value="${target.arch}"/>
        </java>
    </target>

    <target name="download-jre" unless="jre.skip">
        <condition property="target.os" value="windows">
            <isset property="target.os.windows"/>
        </condition>
        <condition property="target.os" value="mac">
            <isset property="target.os.mac"/>
        </condition>
        <property description="target.os default" name="target.os" value="linux"/>
        <property name="jlink.java.target" value="" description="fallback value"/>

        <echo level="info">Downloading and bundling the jre for ${target.os}</echo>
        <java jar="${dist.dir}/${project.filename}.jar" fork="true" failonerror="true">
            <arg value="jlink"/>
            <arg line="--platform ${target.os}"/>
            <arg line="--arch ${target.arch}"/>
            <arg line="--vendor ${jlink.java.vendor}"/>
            <arg line="--version ${jlink.java.version}"/>
            <arg line="--gc ${jlink.java.gc}"/>
            <arg line="--gcversion ${jlink.java.gc.version}"/><!-- openj9 only -->
            <!-- If specified, takes precedence over the above values-->
            <arg line="--targetjdk ${jlink.java.target}"/>
        </java>
    </target>

    <target name="nsis" depends="get-target-arch,target-os-windows,distribute,provision,download-jre,build-exe"/>
    <target name="pkgbuild" depends="get-target-arch,target-os-mac,distribute,provision,download-jre,build-pkg"/>
    <target name="dmg" depends="get-target-arch,target-os-mac,distribute,provision,download-jre,build-dmg"/>
    <target name="makeself" depends="get-target-arch,target-os-linux,distribute,provision,download-jre,build-run"/>


</project>