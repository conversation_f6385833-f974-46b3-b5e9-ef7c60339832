### Compatibility

* :white_check_mark: 2.1 | :white_check_mark: 2.0 | :white_check_mark: 1.9 | [...](Roadmap)

### Background

Many thermal printers (such as Zebra or Epson manufactured printers) need special *raw* printer commands sent to them in order to perform certain functions (such as barcode printing, receipt printing, card printing, etc). These *raw commands* are usually sent as text in a proprietary syntax.  This *raw* syntax is specified by the printer manufacturer (usually in the form of a developer's manual). Syntax will vary drastically between printer manufacturers and printer models.

   > **Note:** Raw commands often do not need the printer driver, and can be sent using the Generic/Text driver supplied with your operating system. (Except USB printers, which sometimes require the device driver along side the Generic/Text driver).

A confusing trait is that even when specifying "raw" mode in the properties of your print driver, most applications won't send the raw printer commands to your printer correctly.  In a simple test case, a Windows workstation printing with Notepad *will not* print raw commands properly.  QZ Tray will.

Raw Printing can be summarized as any text-based printing method.  This may be a dot-matrix printer, this may be an impact printer, however, **in the case of Shipping and Point Of Sale industries, Raw Printing is more commonly a vendor specified programming language that the web developer must understand and adapt for printing thermal labels and receipts.** These labels and receipts are generally Black & White and can have barcodes, logos, bold, centering, etc.  Raw Printing is also capable of doing some advanced features such as cutting receipt paper, kicking out cash drawers, encoding credit cards, etc.  QZ Tray can send these raw commands via all major Desktop web browsers.

***

## Does My Printer Support Raw Printing?  What Raw Languages Should I Look For?
For most printers, look for a technical specification or guide that refers to ZPL, EPL, SBPL, DPL, ESCP, FGL, CPCL, EPCL.  Note, ZPLII, EPL2, ESC/POS are, for the most part, synonymous with their abbreviated counterparts.   If your printer specification lists *ESC/POS Emulation* or *ZPL Emulation*, then Raw Printing will also work.

### My web service provides a PDF... Can I print that?
Yes, but not using Raw Printing.  First, check with your provider if they support one of the raw languages below.  If not, you may need to read [What is PostScript Printing?](what-is-postScript-printing)

### Common Raw Printing Languages:
* ZPL - Zebra Programming Language - *Widely popular in Shipping, Barcoding and Labeling*
* EPL - Eltron Programming Language - *Widely popular in Shipping, Barcoding and Labeling*
* ESCP - Epson Standard Code for Printing - *The defacto standard for Point Of Sales*
* FGL - Friendly Ghost Language - *Popular with Ticket Printing, especially for Boca Printers*
* DPL - Datamax Programming Language - Programming commands used specifically for control of and label production in Datamax-O’Neil(TM) printers - *Popular in Shipping, Barcoding and Labeling*
* SBPL - SATO Barcode Printing Language - *Popular in shipping, Barcoding and Labeling*
* CPCL - Comtec Printer Control Language - *Popular with Mobile Printing*
* EPCL - Eltron Privilege Command Language - *Popular for Magnetic Card Printing*

Language not listed?  Unable to determine?  <NAME_EMAIL>.

### Common Terminology
* DOS Printing - DOS had the capabilities of writing a Raw/Text file directly to a printer using BASIC or output redirection. (e.g `echo file1 > LPT1`). Raw Printing is the equivalent of this. Many programming guides are still written for BASIC programmers despite the language fading in the 1980s.

   > **Note:**  Most DOS functionality has been preserved in modern Windows Operating Systems via the Command Interpreter, cmd.

* UNIX LPR Printing - UNIX and Linux have the capabilities of writing a Raw/Text file directly to a printer using LPR or LPD (e.g `lpr -o raw ...`).   Raw Printing is the equivalent of this.

* Line Printing - A simplified version of Raw/Text printing that generally only accepts lines of text without support for any special commands.

* Hex Printing, CHR Printing - Most Raw Printing Languages require the programmer to send commands using Hexadecimal Codes or ASCII Character codes.  Knowing this in advance and knowing some basic values (e.g. `CHR[27] = &H1B = ESC`) makes reading the programming guides much easier, especially ESCP guides.

## Summary
Raw Printing is still the overwhelmingly preferred method of printing for thermal printers and is the recommended printing method by QZ Industries -- if supported by your hardware.  For further information, please obtain the Programmer's Guide for your specific printer hardware.