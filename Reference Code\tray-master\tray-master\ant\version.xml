<project name="version" basedir="../">
    <!-- Get version information from JAR -->
    <target name="get-version">
        <!-- build.version -->
        <property file="${basedir}/ant/project.properties"/>
        <java jar="${dist.dir}/${project.filename}.jar" fork="true" outputproperty="build.version" errorproperty="build.version.error" timeout="60000" failonerror="true">
            <arg value="--version"/>
        </java>

        <!-- apple.bundleid -->
        <java jar="${dist.dir}/${project.filename}.jar" fork="true" outputproperty="apple.bundleid" errorproperty="apple.bundleid.error" timeout="60000" failonerror="true">
            <arg value="--bundleid"/>
        </java>
        <property description="fallback value" name="build.type" value=""/>
        <property description="fallback value" name="build.version" value=""/>
        <property description="fallback value" name="apple.bundleid" value=""/>

        <echo level="info">
            Version   : ${build.version}${build.type}
            Bundle Id : ${apple.bundleid}
        </echo>
    </target>
</project>