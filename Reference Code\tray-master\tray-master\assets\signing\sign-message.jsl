//
// J# Signing Example
// Echoes the signed message and exits
//

// #########################################################
// #             WARNING   WARNING   WARNING               #
// #########################################################
// #                                                       #
// # This file is intended for demonstration purposes      #
// # only.                                                 #
// #                                                       #
// # It is the SOLE responsibility of YOU, the programmer  #
// # to prevent against unauthorized access to any signing #
// # functions.                                            #
// #                                                       #
// # Organizations that do not protect against un-         #
// # authorized signing will be black-listed to prevent    #
// # software piracy.                                      #
// #                                                       #
// # -QZ Industries, LLC                                   #
// #                                                       #
// #########################################################

module sample

open System
open System.Security.Cryptography
open System.Security.Cryptography.X509Certificates
open System.IO
open System.Text

let request = "test data"

// How to associate a private key with the X509Certificate2 class in .net
// openssl pkcs12 -export -in private-key.pem -inkey digital-certificate.txt -out private-key.pfx
let cert = new X509Certificate2("private-key.pfx")

let sha1 = new SHA512CryptoServiceProvider() // Use "SHA1CryptoServiceProvider" for QZ Tray 2.0 and older

let csp = cert.PrivateKey :?> RSACryptoServiceProvider
let encoder = new ASCIIEncoding()

let data = encoder.GetBytes(request)
let binaryData = csp.SignData(data, sha1)
let output = Convert.ToBase64String(binaryData)

Console.WriteLine output