### Compatibility

* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :white_check_mark: 2.0[*](Pixel-Legacy#deprecation-notice) | :no_entry: 1.9 | [...](Roadmap)

### Background

These features may not work with many [raw printing devices](what-is-raw-printing), such as Zebra, Epson, Citizen printers. The data is sent in Pixel Printing format, which is more common for LaserJet or Deskjet printers.  The `ZDesigner` driver from Zebra allows both raw and pixel printing.

To test these features without a physical printer, use a [printer emulator](printer-emulators#pixel)

### Contents

The following code can be used for Pixel Printing (formerly Postscript Printing) only. If you are unsure what Pixel Printing is, please refer to [What is Pixel Printing?](2.0-pixel-printing)

* [HTML Printing](#html-printing)
   * [Multi-Page HTML Printing](#multi-page-html)
* [PDF Printing](#pdf-printing)
* [Image Printing](#image-printing)
* [Advanced Pixel Printing](#advanced-printing)
 * [Specifying Custom Page Size](#page-size) | [Dymo LabelWriter](#dymo-labelwriter)
 * [Specifying Page Orientation](#orientation)
 * [Overriding Default Margins](#margins)
 * [Disabling Auto-Scaling of Content](#disable-autoscale)
 * [Forcing Image Interpolation](#image-interpolation)
 * [Specifying Custom Job Name](#custom-job-name)
 * [Specifying Tray Used For Printing](#list-printer-trays)
 * [Double sided printing](#duplex-printing)
 * [Vector PDF Printing (Disable Image Rasterization)](#rasterize)
 * [Advanced Print Spooling](#advanced-print-spooling)
* [Chaining Print Requests](#chaining-requests)
* [Queuing Print Requests](#promise-loop)

***

### HTML Printing

1. HTML rendering is done via pure Java in using an embedded webkit browser

   ```js
   var config = qz.configs.create("Printer Name");
   var data = [{
      type: 'pixel',
      format: 'html',
      flavor: 'file', // or 'plain' if the data is raw HTML
      data: 'assets/html_sample.html'
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

1. An [`options` parameter](https://qz.io/api/qz.configs#.setDefaults) can be supplied to to signify things including margins, orientation, size, printer language, encoding, etc.

   ```js
   var config = qz.configs.create("Printer Name", { margins: 2, orientation: 'landscape'});
   var data = [{
      type: 'pixel',
      format: 'html',
      flavor: 'file', // or 'plain' if the data is raw HTML
      data: 'assets/html_sample.html'
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

1. The `pageWidth` or `pageHeight` data [`print` parameter](https://qz.io/api/qz#.print) can be supplied to force a particular HTML page width or HTML page height:

   ### Page Width
   ### Page Height

   ```js
   var config = qz.configs.create("Printer Name");
   var data = [{
      type: 'pixel',
      format: 'html',
      flavor: 'file', // or 'plain' if the data is raw HTML
      data: 'assets/html_sample.html',
      options: { pageWidth: 8.5 /* pageHeight: 11 */ }
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

   ### Multi-Page HTML

   Disable autoscaling to allow the HTML content to overflow to a second page.

   ```js
   var config = qz.configs.create("Printer", { scaleContent: false });
   var data = [{
      type: 'pixel',
      format: 'html',
      flavor: 'file', // or 'plain' if the data is raw HTML
      data: 'assets/html_sample.html'
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

   #### JavaFX
   * :warning: Java 8 + [JavaFX](https://gluonhq.com/products/javafx/) is required for high-resolution (>72 dpi) HTML printing.
     * :warning: Linux users: By default, JavaFX is **NOT** included in OpenJDK
   * :bulb: We recommend using AdoptOpenJDK 11 for all Operating Systems. We have written an installation tutorial: [Upgrading Java](upgrading-java)

### PDF Printing

1. How to print a PDF file

   ```js
   var config = qz.configs.create("Printer Name");
   var data = [{ 
         type: 'pixel',
         format: 'pdf',
         flavor: 'file',
         data: 'assets/pdf_sample.pdf' 
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

#### Base64 PDF

1. How to print a base64 PDF

   ```js
   var config = qz.configs.create("Printer Name");
   var data = [{ 
      type: 'pixel',
      format: 'pdf',
      flavor: 'base64',
      data: 'Ck4KcTYwOQpRMjAzLDI2CkI1LDI2LDAsMUEsMyw3LDE1MixCLCIxMjM0IgpBMzEwLDI2LDAsMywx...' 
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

#### PDF Transparency

* There's a [long-standing issue with Java and transparency](https://issues.apache.org/jira/browse/PDFBOX-4123) that can cause poor print quality and higher memory utilization when a PDF has transparent layers in it.  This issue is perpetuated for low-dpi printers such as 203/300 DPI label printers or 180 DPI receipt printers.

* A workaround for this is to allow QZ Tray to strip out all transparent PDF layers when printing using the `ignoreTransparency` data [`print` parameter](https://qz.io/api/qz#.print).

   ```js
   var config = qz.configs.create("Printer Name");
   );
   var data = [{ 
      type: 'pixel',
      format: 'pdf',
      flavor: 'file'
      data: 'assets/pdf_sample.pdf',
      options: { ignoreTransparency: true } // don't print transparent layers; workaround for PDFBOX-4123, PDFBOX-4380
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

#### PDF Text Quality

* For systems running Windows and printing to low-dpi printers (e.g. 180 DPI, 203 DPI), fonts can become hard to read due to how Java draws vector fonts on these systems (See issue [`#503`](https://github.com/qzind/tray/issues/503))

* A workaround for this is to allow QZ Tray to revert to the PDFBOX 1.8 font rendering using the `altFontRendering` data [`print` parameter](https://qz.io/api/qz#.print).

   ```js
   var config = qz.configs.create("Printer Name");
   );
   var data = [{ 
      type: 'pixel',
      format: 'pdf',
      flavor: 'file'
      data: 'assets/pdf_sample.pdf',
      options: { altFontRendering: true } // use PDFBOX 1.8 font rendering to fix low-dpi fonts
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```


#### PDF Cropbox

1. Since 2.1.1, a [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `bounds` can be used to crop a PDF.  Useful for formats such as USPS labels which were designed for US Letter paper but need to be resized to smaller paper.

   ```js
   var config = qz.configs.create("Printer Name",
      { units: 'in', bounds: { x: 1.2, y: 1.2, width: 6, height: 4 } }
   );
   var data = [{ 
      type: 'pixel',
      format: 'pdf',
      flavor: 'file'
      data: 'assets/pdf_sample.pdf' 
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

#### PDF Page Range

1. Since 2.2.2, a `data` options.[`pageRanges`](https://qz.io/api/qz#.print) parameter can be provided which prints a specific page, select pages or a page range.

   ```js
   var config = qz.configs.create("Printer Name");
   var data = [{ 
      type: 'pixel',
      format: 'pdf',
      flavor: 'file'
      data: 'assets/multi_page_pdf.pdf',
      options: { pageRanges: "1-3" }
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

### Image Printing

1. How to print an image

   ```js
   var config = qz.configs.create("Printer Name");
   var data = [{ 
     type: 'pixel',
     format: 'image',
     flavor: 'file',
     data: 'assets/img/image_sample.png' 
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```
   *  **Shorthand:** `{ type: 'pixel', data: 'assets/img/image_sample.png' }` format and flavor will default to proper values

#### Base64 Image
1. How to print a base64 image

   ```javascript
   var config = qz.configs.create("Printer Name");
   var data = [{ 
      type: 'pixel', 
      format: 'image',
      flavor: 'base64',
      data: 'AAAA...==' 
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

### Advanced Printing

In this section, we will focus on creating a `config` with various options. 


#### Page Size
A page size can be set using the config parameter [`size`](https://qz.io/api/qz.configs#.setDefaults). 

 * Both standard and metric sizes are supported.  Warning, providing a metric page size will assume the [`density`](https://qz.io/api/qz.configs#.setDefaults) is in a metric format as well.

##### Dymo LabelWriter
The following example illustrates how to print a `2.25in x 1.25in` label to a Dymo LabelWriter printer.
 * [`size`](https://qz.io/api/qz.configs#.setDefaults) is provided in inches.  Alternatively `57.15mm x 31.75mm` can be provided if metric units are desired.
 * [`interpolation`](https://qz.io/api/qz.configs#.setDefaults) is provided to prevent barcode blurring.
 * [`colorType`](https://qz.io/api/qz.configs#.setDefaults) is provided to maximize driver quality compatibility/300dpi dithering.

```js
function printStuff() {
var config = qz.configs.create("Printer Name", {
   size: {width: 2.25, height: 1.25}, units: 'in', 
   colorType: 'grayscale', 
   interpolation: "nearest-neighbor" 
});

var data = [{ 
   type: 'pixel',
   format: 'image', //'pdf'
   flavor: 'file',
   data: 'assets/img/image_sample.png' // 'assets/pdf_sample.pdf'
}];

qz.print(config, data).catch(function(e) { console.error(e); });
}
```

#### Orientation
A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `orientation` can be provided to change the default page orientation.  Valid values are `null` (auto), `portrait`, `landscape` and `reverse-landscape`.

If no value is provided, QZ Tray will attempt to determine the orientation automatically for the page and paper size.

```js
var config = qz.configs.create("Printer Name", { orientation: 'landscape' });
var data = [{ 
   type: 'pixel',
   format: 'pdf',
   flavor: 'file',
   data: 'assets/img/pdf_sample.pdf'
}];
qz.print(config, data).catch(function(e) { console.error(e); });
```


#### Margins
A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `margins` can be provided to change the default page margins around the content.
```js
var top = 0.25, right = 0.25, bottom = 0.25, left = 0.25;
var config = qz.configs.create("Printer Name", { margins: { top: top, right: right, bottom: bottom, left: left } });
var data = [{ 
   type: 'pixel',
   format: 'image', //'pdf'
   flavor: 'file',
   data: 'assets/img/image_sample.png' // 'assets/pdf_sample.pdf'
}];
qz.print(config, data).catch(function(e) { console.error(e); });
```

#### Image Interpolation
A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `interpolation` can be provided to change the pixel blending technique used when scaling an image.
```js
var config = qz.configs.create("Printer Name", { interpolation: "nearest-neighbor" });
var data = [{ 
   type: 'pixel',
   format: 'image',
   flavor: 'file',
   data: 'assets/img/image_sample.png'
}];
qz.print(config, data).catch(function(e) { console.error(e); });
```

#### Density
Not providing a density will cause printing to use the default value for the printer. A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `density` can be provided to change the DPI, dots/mm or dots/cm respectively.

```js
// var config = qz.configs.create("Printer Name", { units: "in", density: "best" });  // select best quality (slowest print speed) density automatically
// var config = qz.configs.create("Printer Name", { units: "in", density: "draft" });  // select poorest quality (fastest print speed) automatically
var config = qz.configs.create("Printer Name", { units: "in", density: "600" });  // force 600dpi
var data = [{ 
   type: 'pixel',
   format: 'image', //'pdf'
   flavor: 'file',
   data: 'assets/img/image_sample.png' // 'assets/pdf_sample.pdf'
}];
qz.print(config, data).catch(function(e) { console.error(e); });
```

#### Copies
A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `copies` can be provided to send the specified pixel content multiple times. 

```js
var config = qz.configs.create("Printer Name", { copies: 4 });

var data = [{ 
   type: 'pixel',
   format: 'pdf'
   flavor: 'file',
   data: 'assets/pdf_sample.pdf'
}];;

qz.print(config, data).catch(function(e) { console.error(e); });
```

#### Color
A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `colorType` can be provided to specify if a document is requested to print in black & white or color.  The default is `color`, [regardless of what the OS is set to](https://github.com/qzind/tray/issues/1179). If the OS preference is preferred, provide `default` instead. (Since 2.2.4).

```js
var config = qz.configs.create("Printer Name", { colorType: "grayscale" }); // or "color", "blackwhite"

var data = [{ 
   type: 'pixel',
   format: 'pdf',
   flavor: 'file',
   data: 'assets/pdf_sample.pdf'
}];;

qz.print(config, data).catch(function(e) { console.error(e); });
```


#### Custom Job Name
A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `jobName` can be provided to change the name listed in the print queue.
```js
var config = qz.configs.create("Printer Name", { jobName: "Receipt #123456" });
var data = [{ 
   type: 'pixel',
   format: 'image', //'pdf'
   flavor: 'file',
   data: 'assets/img/image_sample.png' // 'assets/pdf_sample.pdf'
}];
qz.print(config, data).catch(function(e) { console.error(e); });
```

#### List Printer Trays
To fetch the available trays from all printers, use [`qz.printers.details(...)`](https://qz.io/api/qz.printers#.details), and iterate over the resulting `printer.trays` array.
```js
qz.printers.details().then(function(data) {
   data.forEach(function(printer) {
      console.log(printer.name, printer.trays || "(None)");
   });
});
```

#### Define Printer Tray
A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `printerTray` can be provided to specify the destination tray or cartridge used for printing.

```js
var config = qz.configs.create("Printer Name", { printerTray: 1 });
// or
config = var config = qz.configs.create("Printer Name", { printerTray: 'Tray 1' });
var data = [{ 
   type: 'pixel',
   format: 'image', //'pdf'
   flavor: 'file',
   data: 'assets/img/image_sample.png' // 'assets/pdf_sample.pdf'
}];
qz.print(config, data).catch(function(e) { console.error(e); });
```

#### Duplex Printing
For printers that support it, QZ Tray is capable of printing duplex using the [`config`](https://qz.io/api/qz.configs#.setDefaults).  Valid values: `true`, `false`, `'one-sided'`, `'duplex'`, `'long-edge'`, `'tumble'`, `'short-edge'`.

Most printers will use `long-edge` by default when enabled.  Adjust as needed.

```js
var config = qz.configs.create("Printer Name", { duplex: true });
var data = [{ 
   type: 'pixel',
   format: 'pdf',
   flavor: 'file',
   data: 'path/to/my_multiple_page_document.pdf'
}];
qz.print(config, data).catch(function(e) { console.error(e); });
```


#### Disable Autoscale
By default, content is automatically scaled to the destination page size.  This is done as a courtesy to take out the guesswork in fitting the content to the media.  A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `scaleContent` can be forced to prevent auto-scaling.
```js
var config = qz.configs.create("Printer Name", { scaleContent: "false" });  // do not stretch image to page width
var data = [{ 
   type: 'pixel',
   format: 'image', //'pdf'
   flavor: 'file',
   data: 'assets/img/image_sample.png' // 'assets/pdf_sample.pdf'
}];
qz.print(config, data).catch(function(e) { console.error(e); });
   ```

#### Rasterize
In some cases, rather than rasterizing print output, the vector version is preferred.  This can have both quality as well as performance benefits during the printing process.  A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `rasterize` can be forced to prevent software rasterization prior to printing. `rasterize:false` is the default behavior in 2.1.

   * :warning: This feature is currently only effective with PDF printing, which can cause [hard-crashes on OS X](https://issues.apache.org/jira/browse/PDFBOX-2682), use this with caution!

```js
var config = qz.configs.create("Printer Name", { rasterize: "false" });  // use vector rendering
var data = [{ 
   type: 'pixel',
   format: 'image', //'pdf'
   flavor: 'file',
   data: 'assets/img/image_sample.png' // 'assets/pdf_sample.pdf'
}];
qz.print(config, data).catch(function(e) { console.error(e); });
```

### Advanced Print Spooling

Since 2.1.3, [large PDF documents can be split into multiple, smaller documents](https://github.com/qzind/tray/pull/728) by using [`spool.size`](https://qz.io/api/qz.configs#.setDefaults).  This is useful to lessen the time between spooling and printing, as most printers will print spooled documents first.

```js
var config = qz.configs.create("Printer Name", { spool: { size: 10 } });
var data = [{ 
   type: 'pixel',
   format: 'pdf',
   flavor: 'file'
   data: 'assets/some_huge_pdf.pdf' 
}];;

qz.print(config, data).catch(function(e) { console.error(e); });
}
```

### Chaining Requests
Print requests can be chained together to print several documents at once, or to print to several different printers.

```js
var config = qz.configs.create();
var data = [{ type: 'image', data: null, }];

data.data = 'assets/img/my_first_image.png';       ////// First document
config.setPrinter('First Printer');                ////// First printer
qz.print(config, data)

.then(function() {
   data.data = 'assets/img/my_second_image.png';   ////// Second document
   config.setPrinter('Second Printer');            ////// Second printer
   return qz.print(config, data);
})

.then(function() {
   data.data = 'assets/img/my_third_image.png';    ////// Third document
   config.setPrinter('Third Printer');             ////// Third printer
   return qz.print(config, data);
})

.catch(function(e) {
   console.error(e);                // Exceptions throw all the way up the stack
});
   ```

### Promise Loop

Looping multiple configs and multiple data objects is now built-in to the 2.1 API.  More information [here](https://github.com/qzind/tray/issues/161#issuecomment-314610362).