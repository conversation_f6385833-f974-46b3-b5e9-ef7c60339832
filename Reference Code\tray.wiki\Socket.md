### Compatibility

* :white_check_mark: 2.1 | :no_entry: 2.0 | :no_entry: 1.9 | [...](Roadmap)

Since 2.1.3

### Summary

Establish a two-way TCP socket connection using the [`qz.socket`](https://qz.io/api/qz.socket) API.

:warning: Socket API is experimental; API subject to change.

### Opening the socket

```js
qz.socket.open('***********', '80').then(() => {
   console.log("Socket opened");
}).catch(err => {
   console.error("An exception occurred opening the socket", err);
});
```

### Sending data

```js
qz.socket.sendData('***********', '80', "some data!").catch(err => {
   console.error("An exception occurred sending data to the socket", err);
});
```

### Processing Data
```js
qz.socket.setSocketCallbacks(evt => {
   if (evt.type !== 'ERROR') {
      console.log('Socket', evt.host, evt.port, 'received response', evt.response);
   } else {
      console.error(evt.exception);
   }
});
```

### Closing the socket

```js
qz.socket.close('***********', '80').then(() => {
   console.log("Socket closed");
}).catch(err => {
   console.error("An exception occurred closing the socket", err);
});
```