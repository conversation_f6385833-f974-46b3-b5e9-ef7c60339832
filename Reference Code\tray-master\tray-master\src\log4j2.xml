<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
    <Appenders>
        <Console name="console" target="SYSTEM_OUT">
            <PatternLayout pattern="[%p] %d{ISO8601} @ %c:%L%n\t%m%n"/>
        </Console>
    </Appenders>
    <Loggers>
    <!-- Packages -->
        <Logger name="qz" level="trace"/>
        <Logger name="qz.build.provision" level="info"/>
    <!-- Default -->
        <Root level="warn">
            <AppenderRef ref="console"/>
        </Root>
    </Loggers>
</Configuration>
