# Licensing
Revision [1.3](https://github.com/qzind/tray/wiki/Licensing/_history), November 12, 2020

"QZ Tray" is open source software authored by QZ Industries, LLC and published under the LGPL 2.1 license.

### Source Code License
- The exact license text of "QZ Tray" and its components is available here: [LICENSE.txt](https://raw.githubusercontent.com/qzind/tray/master/LICENSE.txt)

### API License
- The [API](https://qz.io/api/), demo code (e.g. `sample.html`) and wiki examples are provided as [Public Domain, no restrictions](https://creativecommons.org/share-your-work/public-domain/) unless noted otherwise.

### Premium Support
- "QZ Tray" binaries provided by https://qz.io ship with a code restriction that encourages a premium support purchase.  This premium license (in the form of an electronic certificate) is available for purchase at https://buy.qz.io.

### Terms Of Use

"Company Branded + Premium Support" is a special rebranding service offered by QZ Industries, LLC released under a special Terms Of Use.
- A description of the "Company Branded + Premium Support" is available here: [Company Branded + Premium Support](Company-Branded)
- The exact Terms Of Use text for the "Company Branded + Premium Support" are available here: [Company Branded + Premium Support](Company-Branded-Service-Terms-Of-Use)

## Support

"QZ Tray" is supported through two options:
* Community Support - Free, public, best effort basis: https://qz.io/support
* Premium Support - Paid, 2-business day response: <EMAIL>