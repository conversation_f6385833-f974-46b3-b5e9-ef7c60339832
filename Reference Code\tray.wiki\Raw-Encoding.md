### Compatibility

* :white_check_mark: 2.1 | :white_check_mark: 2.0 | :no_entry: 1.9 | [...](Roadmap)

### Contents

The following guide is to help understand the techniques available for sending a specialized character encoding to a printer device which does not recognize UTF-8.  This is for [raw printing](what-is-raw-printing) only.

| Language | French | Spanish | Italian | Portuguese | German | Czech | Greek | Vietnamese | Chinese | Japanese | Korean | Arabic | Russian |
|----------|--------|---------|---------|------------|--------|-------|-------|------------|---------|----------|--------|--------|---------|
| [ESCPOS](#escpos) | [French](#escpos-western-european) | [Spanish](#escpos-western-european) | [Italian](#escpos-western-european) | [Portuguese](#escpos-western-european) | [German](#escpos-western-european) | [Czech](#escpos-czech) | [Greek](#escpos-greek) | [Vietnamese](#escpos-vietnamese) | [Simplified](#escpos-simplified-chinese), [Traditional](#escpos-traditional-chinese) | [Japanese](#escpos-japanese) | [Korean](#escpos-korean) | [Arabic](#escpos-arabic) | [Russian](#escpos-russian) |
| [ZPL](#zpl)    |     |         |         |            |        |       |     |            | [Simplified](#zpl-simplified-chinese)         |        |         |           |


### Background

Character encoding, also known as a "codepage" or sometimes "character map" is a raw byte representation of a language's character set.  It's much like a custom font with the letters in (what would appear to be) incorrect places.

Why would someone use a codepage instead of UTF-8?  Well, put simply, most people wouldn't if they had a choice however the thermal printing industry (as well as several others) still has good, reliable hardware in the wild that doesn't offer UTF-8 support.  Chances are if you've received a receipt from a restaurant, store or gas station, it was printed in a raw codepage for your language.  For the US, this is often `CP-1252`, or more formally called `Windows-1252`.

Java 7 supports [about 130 encoding formats](https://docs.oracle.com/javase/7/docs/technotes/guides/intl/encoding.doc.html) which reside in `lib/rt.jar` and `lib/charsets.jar`.  Choosing the correct codepage is often dependent on strict requirements such as hardware limitations but each character set will have some history behind it which may help understand its usefulness to you or your company.

Character sets often start with `CP-xxxx`, `UTF-xxxx`, `IBM-xxxx` or `ISO-xxxx` although may have less-obvious names such as `Big5` (Traditional Chinese), `TCVN-3-1` (Vietnamese), etc.  Also, despite a device claiming a character set is supported, the diacritical marks may be incorrectly implemented for a particular language such as `CP-1258`, or may not render properly due to right-to-left style languages, such as Arabic.

***

### ESCPOS

The following section is written for ESCPOS capable printers.  Due to hardware and firmware differences, not all encodings and fonts are available on all ESCPOS compatible hardware.  Please consult your hardware manufacturer for determining compatibility.

#### ESCPOS Western European
Covers French, Spanish, Italian, Portuguese, German

```js
var config = qz.configs.create("Printer name", { encoding: 'Cp1252' });   // Toggle Cp1252 in Java 
/* If 1252 does not work and you get strange characters, try 'Cp850'; The Epson TM-T20 programming guide, for example, claims to support Cp1252, but it actually supports Cp850*/

var data = [
   /* Toggle WPC1252 in ESCPOS */
   '\x1B' + '\x74' + '\x10',   // ESC t 16 = Cp1252
  // '\x1B' + '\x74' + '\x02',   // ESC t 2 = Cp850
  // '\x1B' + '\x74' + '\x13',   // ESC t 19 = Cp858

   /* Sample French */
   'Voix ambiguë d\'un cœur qui, au zéphyr, préfère les jattes de kiwis\n',

   /* Sample Spanish */
   'Tendré que ir a España. ¿Cómo? Por avion.\n',

   /* Sample Italian */
   'L\'articolo è “uno”. Uno scontrino, perché? Perché la parola inizia per s più consonante.\n',

   /* Sample Portuguese */
   'Luís argüia à Júlia que «brações, fé, chá, óxido, pôr, zângão» eram palavras do português.\n',

   /* Sample German */
   'Köln ist größer als Garmisch Partenkirchen. Der Rhein ist länger als die Mosel.\n',

   /* Line Feeds */
   '\n\n\n\n\n'
];

qz.print(config, data);
```


#### ESCPOS Czech


```js
var config = qz.configs.create("Printer Name", { encoding: 'Cp1250' });

var data = [
   /* Toggle Cp1250 in ESCPOS */
   '\x1B' + '\x74' + '\x2D',   // ESC t 45 = Central European/Eastern European (Cp1250)*
      // * Polish, Czech, Slovak, Hungarian, Slovene, Serbo-Croatian (Latin script), Romanian (before 1993 spelling reform) and Albanian


   /* Sample Czech Text */
   'Ukončete výstup a nástup, dveře se zavírají!\n'
];


qz.print(config, data);
```


#### ESCPOS Greek

```js
var config = qz.configs.create("Printer Name", { encoding: 'Cp1253' });

var data = [
   /* Toggle Cp1253 in ESCPOS */
   '\x1B' + '\x74' + '\x2F',   // ESC t 47 = Greek (Cp1253)
   // '\x1B' + '\x74' + '\x0E',   // ESC t 14 = Greek (Cp737)
   // '\x1B' + '\x74' + '\x0F',   // ESC t 15 = Greek (ISO8859_7)
   // '\x1B' + '\x74' + '\x26',   // ESC t 38 = Greek (Cp869)
   // '\x1B' + '\x74' + '\x0B',   // ESC t 11 = Greek (Cp851) (Cp851 is not be available in Java)

   /* Sample Greek Text */
   'Ταχίστη αλώπηξ βαφής ψημένη γη, δρασκελίζει υπέρ νωθρού κυνός\n'
];

qz.print(config, data);
```

#### ESCPOS Vietnamese
Since `2.0.6`

```js
var config = qz.configs.create(printer, {encoding: 'TCVN-3-1'});

var data = [
   /* Toggle TCVN-3-1 in ESCPOS */
   '\x1B' +  '\x74' + '\x1E', // ESC t 30 = Vietnamese (TCVN-3)

   /* Sample Vietnamese */
   'Tiếng Việt, còn gọi tiếng Việt Nam hay Việt ngữ, là ngôn ngữ của người Việt (người Kinh) và là ngôn ngữ chính thức tại Việt Nam.\n'
];

qz.print(config, data);
```

#### ESCPOS Simplified Chinese
Covers Simplified (Mandarin) Chinese

```js
var config = qz.configs.create("Printer Name", {encoding: 'GB2312'});  // *Epson T88 models with correct firmware.  Toggles EUC encoding, Simplified Chinese
                                            // {encoding: 'GBK'});     // Fuken POS90 ships with GB18030, but 'GBK' is required

var printData = [
   '\x1B' + '\x40',  // ESC @ - init command, necessary for proper byte interpretation
   '这句话是简体中文',
   '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A',
   '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A',
   '\x1B' + '\x69'          // cut paper
];

qz.print(config, printData);
```
> \*Epson TM-T88VI and higher has board capable of firmware update.  TM-T88V and lower require special model printer.  Please contact official Epson reseller for firmware; incorrect firmware may damage printer. Printer status will read `Resident Character: Alphanumeric, Simple Chinese`

#### ESCPOS Korean
Only supported for certain hardware.  Contact Epson support if unsure.

```js
var config = qz.configs.create("Printer Name", { encoding: 'EUC_KR' });  // Instruct Java to use EUC_KR

var printData = [
   '\x1B' + '\x40',   // ESC @ - init command, necessary for proper byte interpretation
   '\x1B' + '\x52' + '\x13', // Select international character set 13
   '국가안전보장에 관련되는 대외정책·군사정책과 국내정책의 수립에 관하여 국무회의의 심의에 앞서 ',
   '대통령의 자문에 응하기 위하여 국가안전보장회의를 둔다. 국민의 모든 자유와 권리는 국가안전보장·질서유지 ',
   '또는 공공복리를 위하여 필요한 경우에 한하여 법률로써 제한할 수 있으며, 모든 권력은 국민으로부터 나온다. ',
   '형사피고인이 스스로 변호인을 구할 수 없을 때에는 법률이 정하는 바에 의하여 국가가 변호인을 붙인다.',
   '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A',
   '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A',
   '\x1B' + '\x69'          // cut paper
];

qz.print(config, printData);
```

#### ESCPOS Traditional Chinese
Covers Traditional (Cantonese) Chinese

```js
var config = qz.configs.create("Printer Name", {encoding: 'Big5'});  // *Epson T88 models with correct firmware.  Toggles Big5 with Hong Kong extensions.
                                            // {encoding: 'GBK'});   // Fuken POS90 ships with GB18030, but 'GBK' is required

var printData = [
   '\x1B' + '\x40',   // ESC @ - init command, necessary for proper byte interpretation
   '艾德蒙 AOC E2450SWH 23.6吋\n',
   ' LED液晶寬螢幕特價$ 19900',
   '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A',
   '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A',
   '\x1B' + '\x69'          // cut paper
];

qz.print(config, printData);
```
> \*Epson TM-T88VI and higher has board capable of firmware update.  TM-T88V and lower require special model printer.  Please contact official Epson reseller for firmware; incorrect firmware may damage printer. Printer status will read `Resident Character: Alphanumeric, Traditional Chinese`

#### ESCPOS Japanese
##### ESCPOS Japanese UTF8

:warning: UTF8 is limited to certain model printers.  See [Shift-JIS](#escpos-japanese-shift-jis) for older models.

```js
var config = qz.configs.create("Printer Name", {encoding: 'UTF8'});  // Epson models which support Unicode encoding

var printData = [
   '\x1B' + '\x40',          // ESC @ - init command, necessary for proper byte interpretation
   '\x1C' + '\x28' + '\x43' + '\x02' + '\x00' + '\x30' + '\x02', // FS ( C <Function 48> - Select character encoding (unicode/UTF8) system

   'こんにちは',               // "Konnichiwa"

   '\n\n\n\n\n\n\n',         // several line feeds
   '\n\n\n\n\n\n\n',         // more line feeds

   '\x1B' + '\x69'           // cut paper
];

qz.print(config, printData);
```

##### ESCPOS Japanese Shift-JIS
:warning: Printer must first be set to "Japanese" mode using button-press method or "EPSON Printer Model Setting Utility".  For non-Japanese models, Shift-JIS support may require obtaining and installing a font file from EPSON. 

```js
var config = qz.configs.create("Printer Name", {encoding: 'SJIS'});  // Instruct Java to use Shift-JIS encoding

var printData = [
   '\x1B' + '\x40',          // ESC @ - init command, necessary for proper byte interpretation

   '\x1C' + '\x26',          // FS & - select kanji character mode, use FS . to revert to ANK mode (ANK is default on startup for many printers)
   '\x1C' + '\x43' + '\x02', // FS C - select a character kanji code system - \x02 = Shift_JIS-2004

   'こんにちは',               // "Konnichiwa"

   '\n\n\n\n\n\n\n',         // several line feeds
   '\n\n\n\n\n\n\n',         // more line feeds

   '\x1B' + '\x69'           // cut paper
];

qz.print(config, printData);
```

#### ESCPOS Arabic
Since `2.0.8`

Instruct an Epson printer to use IBM864 codepage.  Note, this lacks most of the Arabic diacritics which are used in Modern Standard Arabic as well as Dialectal Arabic as well as some additional letters, however, these are often omitted, so IBM864 should be suitable for Arabic characters.

For the Epson printer to recognize IBM864, we had to use a [utility to change settings in the printer](https://user-images.githubusercontent.com/6345473/42548380-c0e2034c-8494-11e8-8ffe-15bb3563583a.png).  More details about this is available here: [#339](https://github.com/qzind/tray/pull/339)

```js
var config = qz.configs.create("Printer Name", { encoding: "IBM864" });  // Toggle IBM864 in Java 

var printData = [
   '\x1B' + '\x40',   // ESC @ - init command, necessary for proper byte interpretation
   '\x1B' + '\x74' + '\x25', // Setup "codepage 37", which is Epson's IBM864
   'لكن لا بد أن أوضح لك أن كل هذه الأفكار', '\n', // UTF-8 RTL text
   '\x1B' + '\x69'          // cut paper
];

qz.print(config, printData);
```


#### ESCPOS Russian

```js
var config = qz.configs.create("Printer Name", { encoding: 'Cp866' });

var data = [
   /* Toggle Cp866 in ESCPOS */
   '\x1B' + '\x74' + '\x11',   // ESC t 17 = Cyrillic #2 (Cp866)

   /* Sample Russian Text */
   'Быстрая коричневая лиса прыгает через ленивую собаку.\n'
];

qz.print(config, data);
```
### ZPL

#### ZPL Simplified Chinese
Requirements:
* Zebra Printers using Unicode compatible x.14 firmware (or later) and the 64MB Flash Memory Option
* A Unicode compatible font set for printing Simplified Chinese characters is installed - for more information on installing fonts on a Zebra printer, refer to [Installing Fonts on a Zebra Printer](https://qz.io/wiki/Installing-Fonts-on-a-Zebra-Printer)
    * This example uses the Andale Simplified Chinese font set (P/N 56080-001)
> **Note:** The printer will not permanently store the Font Assignment command (^CW) or Encoding table command (^SE) -- if the printer powers off, these settings are lost. Send both of these commands at the beginning of every format or you can use an Autoexec file, which automatically sends these settings when the printer is powered on: ``^XA^DFE:AUTOEXEC.ZPL^CW1,E:ANMDS.TTF^SEE:GB.DAT^XZ``

```js
var config = qz.configs.create("PrinterName");

var data = [
   '^XA\n',
   '^SEE:GB.DAT^CI28\n', //^SE = set the encoding table & location; ^CI28 = change encoding to UTF-8
   '^CW1,E:ANMDS.TTF\n',     //^CW = set the font identifier & location (Andale font)
   '^FO50,50\n',
   '^A1,50,50\n'
   '^FD Chinese Andale 盒呆艾丙^FS\n',
   '^FO50,200\n',
   '^A1,50,50\n',
   '^FD Zebra 盒呆艾丙^FS\n',
   '^XZ\n'
];

qz.print(config, data);
}
```