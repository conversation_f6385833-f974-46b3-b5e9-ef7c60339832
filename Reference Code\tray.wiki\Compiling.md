### Compatibility
* :white_check_mark: 2.1 | :white_check_mark: 2.0 | :white_check_mark: 1.9 | [...](Roadmap)

### Steps

 1. First, install dependencies (`jdk`, `git`, `ant`, `nsis/makeself`) per [Install Dependencies](install-dependencies)

 1. <PERSON>lone the source code per [Clone Source Code](install-dependencies#clone-source-code)

 1. Pull in the latest source code
    ```bash
    cd tray
    git pull
    ```
 
 1. Compile
    ```bash
    ant
    ```
    **Optional:** Start the software up using [this command](faq#debugging-qz-tray).
 
 1. Package
    ```bash
    ant nsis       # <-- Windows installer
    ant pkgbuild   # <-- Apple installer (macOS only)
    ant makeself   # <-- Linux installer
    ```
    > **Note:**  The installer will be placed in `./out/qz-tray-x.x.x.x`, (i.e. `.exe`, `.run`, `.pkg`)


 1. Build for another architecture:
    ```bash
    # Intel 64-bit
    ant -Dtarget.arch=x86_64  # nsis|pkgbuild|makeself

    # ARM 64-bit
    ant -Dtarget.arch=arm64   # nsis|pkgbuild|makeself

    # RISC-V 64-bit
    ant -Dtarget.arch=riscv64 # nsis|pkgbuild|makeself
    ```

---
 
### IntelliJ
 
 1. Download and install IntelliJ Community Edition from https://www.jetbrains.com/idea/download/
 1. Launch IntelliJ
 1. Open the project
 1. Switch to project view using `ALT + 1` (or `Command + 1` on Mac)
 1. Click **File, Project Structure**
    * If you don't have a JDK installed, IntelliJ will offer to download this automatically.  QZ Tray recommends the BellSoft Liberica SDK (The "standard" version, NOT the "full" version).
    * If you do have a JDK installed, check that the Project SDK is correct.  We recommend Java 11 LTS however newer versions will also work.
    * If `<No SDK>`, click New, JDK and browse to the appropriate install location, e.g. `C:\Program Files\<vendor>\openjdk 11.x.x`
 1. From the Project Explorer, Navigate to:
    * `tray`, `src`, `qz`, `App.java`
    * Right Click, Run
    * On Windows, a firewall prompt may appear, click Run
 1. If you receive JavaFX errors, close and reopen the project. This startup jobs needs the JDK to run, so catch-22. :)
 1. Exit App.java by locating it in the System Tray, Right Click, Exit
    * Alternately, you can click **Stop** within IntelliJ from bottom left "Run" tab

---

### Advanced

#### Clone the project directly from IntelliJ
 1. When prompted, click **Check out from Version Control (GitHub)**
 
    **Host:** `github.com`<br>
    **Auth type:** `password`<br>
    **Login:** `<github username>`<br>
    **Password:** `<github password>`<br>

 1. Clone Repository
 
    **Git Repository URL:** `https://github.com/qzind/tray`<br>
    **Parent Directory:** `<leave default, usually "C:\Users\<USER>\IdeaProjects">`<br>
    **Directory Name:** `<leave default, "tray">`<br>
    Note, if the Parent Directory doesn't exist, create it.

#### ANT Command Line
   * Override internal certificate used for signature validation*

      ```bash
      ant nsis -Dauthcert.use=path/to/override.crt
      ```
      *Since 2.0.2

#### ANT Properties File
   * Common uses:
     * Signing a Windows installer executable
     * Providing persistent ant property overrides

   * Create `tray/.../private/private.properties`

      ```properties
      hsm.storetype=AWS
      hsm.keystore=us-east-1
      hsm.alias=my-signing-alias
      # Must be formatted accesskey|secretaccesskey
      hsm.storepass=MY_ACCESS_KEY|MY_SECRET_KEY
      hsm.certfile=../private/full-chain.pem
      hsm.tsaurl=http://my.timestamp.authority
      hsm.algorithm=SHA-256
      #authcert.use=#path to signing auth cert
      ```

#### Troubleshooting

   * `Error:(3, 24) java: cannot access javafx.animation.PauseTransition`
   * If IntelliJ complains `package sun.awt does not exist`, [see the workaround here]( https://stackoverflow.com/a/56388149/3196753)