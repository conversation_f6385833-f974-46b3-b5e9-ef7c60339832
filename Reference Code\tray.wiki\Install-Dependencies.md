### Compatibility

* :white_check_mark: 2.1 | :white_check_mark: 2.0 | :white_check_mark: 1.9 | [...](Roadmap)

### JDK
 * QZ Tray 3.0 - JDK21 or higher (Liberica OpenJDK21 recommended)
 * QZ Tray 2.2 - JDK11 or higher (Liberica OpenJDK11 HotSpot recommended)
 * QZ Tray 2.1 - JDK8 or higher (AdoptOpenJDK11 HotSpot recommended)
 * QZ Tray 2.0 - JDK7 or higher (AdoptOpenJDK11 HotSpot recommended)

### Contents

 * [Windows](#windows)
 * [Mac](#mac)
 * [Ubuntu](#ubuntu)
 * [Fedora](#fedora)
 * [Cloning Source Code](#clone-source-code)
 * [Compiling](Compiling)

### Windows
1. Install [:warning:](#jdk)**JDK 7** or higher:

   http://www.oracle.com/technetwork/java/javase/downloads/
1. Download and extract **Apache Ant**:

   https://ant.apache.org/bindownload.cgi
1. Setup **environment variables** `JAVA_HOME`, `PATH`, etc:
   * Assume Ant is installed in `c:\ant\`. The following commands set up the environment:
     ```
      set ANT_HOME=c:\ant
      set JAVA_HOME=c:\jdk1.7.0_51
      set PATH=%PATH%;%ANT_HOME%\bin
     ```
   http://ant.apache.org/manual/install.html#setup
1. Install **NSIS 3.0+**:

   http://nsis.sourceforge.net/Download
1. Install **git**:

   https://git-scm.com/download/win

### Mac
 
1. Install command line tools for macOS (installation methods vary)

1. Install [:warning:](#jdk)**JDK 7** or higher via: http://www.oracle.com/technetwork/java/javase/downloads/

1. Install **homebrew** via:

   ```bash
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   ```
1. Install **brew dependencies** via:

   ```bash
   brew install git ant makeself nsis
   ```

1. If you prefer to use a system-installed version of Java, you can use `brew install ant --ignore-dependencies`

1. Install **git**:

   https://git-scm.com/download/mac

### Ubuntu

1. Install AdoptOpenJDK11 via https://adoptopenjdk.net/installation.html#linux-pkg
   * **Note:** You can use OpenJDK 8+ via apt instead if desired

1. Install **remaining dependencies** (ant, nsis, makeself) via:

   ```bash
   sudo apt-get install git ant nsis makeself
   ```
 
### Fedora
1. Install AdoptOpenJDK11 via https://adoptopenjdk.net/installation.html#linux-pkg
   * **Note:** You can use OpenJDK 8+ via yum instead if desired

1. Install **remaining dependencies** (ant, nsis, makeself) via:

   ```
   sudo yum install git ant nsis makeself
   ```

---

### Clone Source Code

1. Clone the repository:

* QZ Tray
   ```bash
   git clone https://github.com/qzind/tray
   ```

    > **Note:** This will clone the source code for the default branch of the software.  To clone a non-default branch, you would need to specify that branch name i.e. `-b foo`

---

### Compile

1.  See separate [compiling](Compiling) tutorial.