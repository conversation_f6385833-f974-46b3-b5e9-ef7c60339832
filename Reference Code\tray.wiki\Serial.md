### Compatibility

* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :no_entry: 2.0 | :no_entry: 1.9 | [...](Roadmap)

### Summary

Read and write data to a serial, parallel or virtual serial port.

### List Serial Ports

```js
qz.serial.findPorts().then((ports) => {
   console.log(ports);
}).catch(displayError);
```

### Open and Close Ports

* Open a serial port: [`openPort(port, bounds)`](https://qz.io/api/qz.serial#.openPort)

   ```js
   var properties = {
      rx: {
         start: '\x02',
         end: '\x0D',
         width: null
      },

      baudRate: 9600,
      dataBits: 8,
      stopBits: 1,
      parity: 'NONE',
      flowControl: 'NONE'
   };

   // WARNING:  Since 2.0.7 baud properties should be set here in openPort()
   qz.serial.openPort('COM1', properties).then(() => { // or '/dev/ttyUSB0', etc
       console.log('COM1 opened');
   }).catch((err) => {
       console.error(err);
   });
   ```
   * **Note:**  For versions 2.0 and older, the properties syntax does not use the `rx { }` object, but instead uses [`start`, `end`, `width`](https://qz.io/api/qz.serial#.openPort).  Backwards support for the 2.0 syntax is [deprecated](https://github.com/qzind/tray/issues/820) in 2.1 versions.

* Close a serial port [`closePort(port)`](https://qz.io/api/qz.serial#.closePort)

   ```js
   qz.serial.closePort('COM1').then(() => {  // or '/dev/ttyUSB0', etc
       console.log('COM1 closed');
   }).catch((err) => {
       console.error(err);
   });
   ```

### Send String Data
* Send data to the open serial port via [`sendData(port, data, properties)`](https://qz.io/api/qz.serial#.sendData)
   
   **Note:** The default [`sendData()`](https://qz.io/api/qz.serial#.sendData) settings must be changed for [Mettler Toledo](#mettler-toledo) scales
   
   ```js
   var data = 'hi, serial port\n'; // or since 2.1.1: { type: 'plain', data: '...' }
   
   // WARNING:  sendData() properties are deprecated in 2.0.7.  Baud properties should be set in openPort() instead.
   qz.serial.sendData('COM1', data).catch(displayError);
   ```

### Send Hex Data
Since 2.1.1
* Send data to the open serial port via [`sendData(port, data, properties)`](https://qz.io/api/qz.serial#.sendData)
   
   ```js
   var data = {
      type: 'hex',
      data: 'x68x65x6Cx6Cx6Fx0A' // 'hello\n'
   };
   
   qz.serial.sendData('COM1', data).catch(displayError);
   ```

### Send File Data
Since 2.1.1
* Send data to the open serial port via [`sendData(port, data, properties)`](https://qz.io/api/qz.serial#.sendData)
   
   ```js
   var data = {
      type: 'file',
      data: 'mydirectory/myfile.txt'
   };
   
   qz.serial.sendData('COM1', data).catch(displayError);
   ```

### Send Base64 Data
Since 2.1.1
* Send data to the open serial port via [`sendData(port, data, properties)`](https://qz.io/api/qz.serial#.sendData)
   
   ```js
   var data = {
      type: 'base64',
      data: 'aGVsbG8K' // 'hello\n'
   };
   
   qz.serial.sendData('COM1', data).catch(displayError);
   ```

 #### Mettler Toledo

 The default settings need to be changed for serial communication to work with Mettler Toledo scales.  For other scale types, see [issue `#1158`](https://github.com/qzind/tray/issues/1158#issue-1814369173).

   ```js
   var data = 'W\n';   // <--- Weight command - Also works with 'W\r'

   var properties = {
      rx: {
         start: '\x02',
         end: '\x0D',
         width: null
      },

      baudRate: 9600,
      dataBits: 7,     // <--- Changed from 8
      stopBits: 1,
      parity: 'EVEN',  // <--- Changed from NONE
      flowControl: 'NONE'
   };

   // WARNING:  sendData() properties are deprecated in 2.0.7.  Baud properties should be set in openPort() instead.
   qz.serial.sendData('COM1', data, properties).catch(displayError);
   ```

### Optima

Optima scale requires the scale configured for "Command request method", which is often stored in setting `C18` -> `3`.  A key combination (e.g. <kbd>↵ PRINT</kbd> + <kbd>↱ HOLD</kbd>) will enter settings mode.  <kbd>⍐ ACCUM</kbd> will save and exit.

   | Command | Name | Function    |
   |---------|------|-------------|
   | T | Tare    | Save and clear tare                   |
   | Z | Zero    | Zero gross weight                     |
   | P | Print   | Print the weight                      |
   | R | G.W/N.W | Read gross weight or net weight       |
   | C | Kg/lb   | Kg/lb conversion                      |
   | G | G.W     | Check gross weight at net weight mode |


   ```js
   var data = 'R\n';   // <--- Read gross weight or net weight

   var properties = {
      rx: {
         start: '\x02',
         end: '\x0D',
         width: null
      },

      baudRate: 9600,
      dataBits: 8,
      stopBits: 1,
      parity: 'NONE',
      flowControl: 'NONE'
   };

   qz.serial.openPort('COM1', properties).then(() => { // or '/dev/ttyUSB0', etc
       console.log('COM1 opened');
   }).then(() => {
       return qz.serial.sendData('COM1', data);
   }).catch((err) => {
       console.error(err);
   });
   ```

### Process Results

1. Use a callback for processing the data returned from the serial port.

   ```js
   qz.serial.setSerialCallbacks((evt) => {
      if (evt.type !== 'ERROR') {
         console.log('Serial', evt.portName, 'received output', evt.output);
      } else {
         console.error(evt.exception);
      }
   });
   ```

#### Troubleshooting

##### Linux Permission Denied

If no ports will list or if you receive `Permission denied` in Linux ([bugs.launchpad.net#949597](https://bugs.launchpad.net/ubuntu/+source/gtkterm/+bug/949597))

```
Error: Port name - /dev/ttyUSB0; Method name - openPort(); Exception type - Permission denied.
```

1. Run the following command:

   ```bash
   sudo usermod -a -G dialout "$USER"
   ```
1. Log out of the desktop (or reboot)
1. Log in to the desktop, try again

