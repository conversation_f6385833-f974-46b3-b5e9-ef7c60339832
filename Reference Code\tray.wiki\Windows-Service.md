* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :white_check_mark: 2.0 | :white_check_mark: 1.9 | [...](Roadmap)

### Objective

* Run QZ Tray as a Windows service.<br>
   <img src="https://user-images.githubusercontent.com/6345473/69070661-8834fc00-09f6-11ea-9a8c-cea419e9583b.png" width="800px"/>
* This configuration utilizes a third party product "NSSM" to register QZ Tray as a service.  It's not officially supported, but has been reported to work well by several users/environments.

### Prerequisites

* NSSM Service Manager: https://nssm.cc/download (**Important:** The "Prerelease" version is required)

### Steps

#### Prepare QZ Tray
1. If upgrading from QZ Tray version 2.x to 2.2, the [old service must be removed](#remove-the-service)
1. **Warning:** Services can NOT show the "Trusted" dialog.  If not already...
   * Visit a website with the certificate that needs to be whitelisted.
   * Click "Remember this decision".  This will save a file `allowed.dat` to `%APPDATA%\qz`.
1. Open a command prompt **As Administrator**
1. Create a `.bat` file that can launch QZ Tray (the below commands will create this file)
   * QZ Tray 2.2 and newer (Bundled Java)
      ```bat
      echo wmic.exe process where "Name like '%%java%%' and CommandLine like '%%qz-tray.jar%%'" call terminate>"%PROGRAMFILES%\QZ Tray\qz-tray.bat"
      echo "%PROGRAMFILES%\QZ Tray\runtime\bin\java.exe" -Xms512M -jar "%~dp0qz-tray.jar" %*>> "%PROGRAMFILES%\QZ Tray\qz-tray.bat"
      ```
   * QZ Tray 2.1 and lower
      ```bat
      echo wmic.exe process where "Name like '%%java%%' and CommandLine like '%%qz-tray.jar%%'" call terminate>"%PROGRAMFILES%\QZ Tray\qz-tray.bat"
      echo java.exe -Xms512M -jar "%~dp0qz-tray.jar" %*>> "%PROGRAMFILES%\QZ Tray\qz-tray.bat"
      ```
  
1. Disable auto-start of QZ Tray for ordinary users:
   ```bat
   echo 0 > "%PROGRAMDATA%\qz\.autostart"
   ```
1. Make a location for SYSTEM certificate whitelist
   ```bat
   mkdir "%WINDIR%\System32\config\systemprofile\AppData\Roaming\qz"
   ```
1. Copy the `allowed.dat` to the SYSTEM profile
   ```bat
   copy /Y "%APPDATA%\qz\allowed.dat" "%WINDIR%\System32\config\systemprofile\AppData\Roaming\qz\allowed.dat"
   ```
   * 32-bit Java will need to change this to `%WINDIR%\SysWOW64` instead.

#### Install the Service
1. Open a command prompt **As Administrator**
1. If not already, extract NSSM to `C:\` (e.g. `C:\nssm`)
1. Change to the `win64` directory within NSSM.
   ```bat
   cd C:\nssm\win64
   ```
1. Install the `QZ Tray` service
   ```bat
   nssm install "QZ Tray" "%PROGRAMFILES%\QZ Tray\qz-tray.bat"
   nssm set "QZ Tray" Description "Browser printing utility"
   ```
1. Start the service
   ```bat
   net start "QZ Tray"
   ```
1. Check that it's running, look for `QZ Tray` in the services listing.
   ```bat
   start services.msc
   ```
1. That's it, the `QZ Tray` service has been successfully installed.

### Advanced

#### Run with a dedicated Java version
1. Before making any changes, make sure to stop the service
   ```bat
   net stop "QZ Tray"
   ```
1. Run QZ Tray with a custom Java version (**Important:** change `C:\Program Files\Java\jdk1.8.0_231\bin` to point to a proper JDK)
   ```bat
   echo set PATH=C:\Program Files\Java\jdk1.8.0_231\bin;^%PATH^%> "%PROGRAMFILES%\QZ Tray\qz-tray.bat"
   echo wmic.exe process where "Name like '%%java%%' and CommandLine like '%%qz-tray.jar%%'" call terminate>>"%PROGRAMFILES%\QZ Tray\qz-tray.bat"
   echo java.exe -Xms512M -jar "%~dp0qz-tray.jar" %*>> "%PROGRAMFILES%\QZ Tray\qz-tray.bat"
   ```

#### Remove the Service
1. Open a command prompt **As Administrator**
1. Change to the `win64` directory within NSSM.
   ```bat
   cd C:\nssm\win64
   ```
1. Uninstall the `QZ Tray` service
   ```bat
   net stop "QZ Tray"
   nssm remove "QZ Tray"
   ```
1. Re-enable autostart for regular users
   ```bat
   del "%PROGRAMDATA%\qz\.autostart"
   ```

### Troubleshooting
1. Make sure you can call `java` from a command prompt
   ```bat
   java -version
   ```
   * If `java` is not available, you may hardcode the path using [Advanced](#advanced) section above.

2. If initial connection works, but printing, listing printers, fails, make sure you've properly configured signing. [Signing](Signing)
