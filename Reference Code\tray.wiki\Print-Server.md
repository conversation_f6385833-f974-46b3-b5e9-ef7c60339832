### Compatibility
* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :white_check_mark: 2.0 | :white_check_mark: 1.9 | [...](Roadmap)

### Objective
 * Run QZ Tray as a dedicated service, useful for machines that cannot have QZ Tray installed (such as a Terminal Services/Citrix environment and mobile/sandboxed devices such as iOS, Android, ChromeOS).<br><br>
   **Note:** If you're looking for step-by-step instructions for installing QZ Tray as a [Windows service, please see this tutorial](Windows-Service) instead.

### Steps
The steps will be the same for each platform, however, the way you regenerate the certificate and install it on the client will vary.

1. [Install QZ Tray](#install-qz-tray)
1. [Connect to QZ Tray](#connect-to-qz-tray)
1. [Remove localhost Bindings](#remove-localhost-bindings)
1. [Copy Files to the Client](#copy-files-to-client)
1. [Install the new certificate on the client](#certificate-installation)
   * [Windows](#windows-1)
   * [Linux](#linux-1)
   * [macOS](#macOS-1)
   * [iOS (iPad, iPhone)](#ios-ipad-iphone)
   * [Android](#android-using-chrome-browser-pixel-moto-samsung-et-cetera-smart-devices)
1. [Import Firefox Certificates](#import-firefox-certificates)
1. [Verify secure connection](#verify-secure-connection)

### Install QZ Tray
Install QZ Tray onto the server via the desktop installer.  Refer to [this guide](using-qz-tray) if unsure how to do this.

### Connect to QZ Tray
By default, the `qz.websocket.connect();` functions are bound to `localhost`.  This can be overridden using the demo page or by using code.

 > **Note:** `foo.bar` is the hostname of the server.

* If using the demo page, expand the area under the connect button
   
   <img width="384" alt="image" src="https://github.com/user-attachments/assets/78dd88e4-95f1-4fec-95f6-7a080b0e4523" />

* If using code, replace all calls to `qz.websocket.connect();` with `qz.websocket.connect({host: "foo.bar"});`
 
   ```js
   //qz.websocket.connect();
   qz.websocket.connect({host: "***********"});
   ```

<details>
<summary>Deprecated</summary>

#### 1.9
* Replace all calls to `deployQZ` with `deployQZ("foo.bar");` 
 
   ```js
   //deployQZ();
   deployQZ("***********");
   ```

</details>

That's it! At this point, an HTTP connection can be made from a client to the server running QZ Tray.  The steps below illustrate how to establish an HTTPS connection.

## HTTPS Support

### Trusted CA Certificate

* Organizations wishing to use a 3rd-party Trusted CA SSL certificate can do so by manually shimming it into the software.  If you do not have or do not wish to use a CA certificate, please see [remove localhost bindings](#remove-localhost-bindings) section instead. 

   ```bash
   java -jar qz-tray.jar certgen --key "privkey.pem" --cert "fullchain.pem"
   
   # or for a pfx/pkcs#12 file
   java -jar qz-tray.jar certgen --pfx "mycert.pfx" --pass "12345"
   
   # ... or for 2.0 please see https://github.com/qzind/tray/pull/89
   ```

### Remove Localhost Bindings
**HTTPS:** The steps provided in this section illustrate how to create a self-signed certificate for a secure (HTTPS) connection.  These steps are not needed for connections made via HTTP or connections that use a [3rd-party Trusted CA Certificate file](#trusted-ca-certificate).
 
For secure websockets support (HTTPS), QZ Tray generates a certificate for `localhost` traffic at install time called `root-ca.crt`.  In order for QZ Tray to run on a separate machine, the `hostname` (usually "localhost") must exist in the certificate.  This is an override which is done by running a custom script.

1. Re-run the certificate generation (`"foo.bar"` is the hostname that QZ Tray will run on). If you require Firefox HTTPS support, Firefox needs to be installed prior to this step.
   * This will re-generate the `qz-tray.properties`, `root-ca.crt` and `qz-tray.jks`

   #### Windows
   Open a command prompt **as an administrator** and run the following commands:

   #### Windows (QZ Tray 2.2+)
   ```cmd
   REM Regenerate the certificate
   cd "%PROGRAMFILES%\QZ Tray\"
   qz-tray-console.exe certgen --host "***********"
   REM -- OR --
   qz-tray-console.exe certgen --hosts "domain1;domain2;..."
   ```

   #### Windows (QZ Tray 2.1)*
   ```cmd
   REM Regenerate the certificate
   cd "%PROGRAMFILES%\QZ Tray\"
   java -jar qz-tray.jar certgen --host "***********"
   REM -- OR --
   java -jar qz-tray.jar certgen --hosts "domain1;domain2;..."
   ```
   >  *Since 2.1.0-RC9
   #### Windows (QZ Tray 2.0)
   ```cmd
   REM Regenerate the certificate
   cd "%PROGRAMFILES%\QZ Tray\"
   cscript auth\windows-keygen.js . install "foo.bar"
   ```
   ![image](https://cloud.githubusercontent.com/assets/12505463/11433576/d5bae20c-948c-11e5-97a7-c6135c7728ef.png)


   #### Linux

   #### Linux (QZ Tray 2.2+)
   ```bash
   # Regenerate the certificate
   sudo /opt/qz-tray/qz-tray certgen --host "***********"
   # -- OR --
   sudo /opt/qz-tray/qz-tray certgen --hosts "domain1;domain2;..."
   ```

   #### Linux (QZ Tray 2.1)*
   ```bash
   # Regenerate the certificate
   cd /opt/qz-tray
   sudo java -jar qz-tray.jar certgen --host "***********"
   # -- OR --
   sudo java -jar qz-tray.jar certgen --hosts "domain1;domain2;..."
   ```
   >  *Since 2.1.0-RC9
   #### Linux (QZ Tray 2.0)
   ```bash
   # Reinstall completely with specified hostname
   sudo bash qz-tray-2.0.x.run -- -y "foo.bar"
   ```

   #### macOS

   #### macOS (QZ Tray 2.2+)
   ```bash
   # Regenerate the certificate
   sudo "/Applications/QZ Tray.app/Contents/MacOS/QZ Tray" certgen --host "***********"
   # -- OR --
   sudo "/Applications/QZ Tray.app/Contents/MacOS/QZ Tray" certgen --hosts "domain1;domain2;..."
   ```

   #### macOS (QZ Tray 2.1)*
   ```bash
   # Regenerate the certificate
   cd "/Applications/QZ Tray.app/"
   sudo java -jar qz-tray.jar certgen --host "***********"
   # -- OR --
   sudo java -jar qz-tray.jar certgen --hosts "domain1;domain2;..."
   ```
   > *Since 2.1.0-RC9
   #### macOS (QZ Tray 2.0)
   ```bash
   # Regenerate the certificate
   sudo /Applications/QZ\ Tray.app/auth/apple-keygen.sh install "foo.bar"
   
   # Optionally, regenerate the Firefox configuration files
   sudo /Applications/QZ\ Tray.app/auth/firefox/firefox-cert.sh install "foo.bar"
   ```

4. Relaunch QZ Tray. ***This must be done every time you regenerate a certificate.***

> **Note:** Alternatively, you may provide an custom `.properties` file directly to Java using `-DsslPropertiesFile=path/to/my.properties`.

### Copy Files to Client

1. For iOS (iPad, iPhone), skip to the [iOS section below](#ios-ipad-iphone)

1. Copy `root-ca.crt` from the server's QZ Tray shared directory to the client
   > This is in **Advanced, Troubleshooting, Browse Shared Folder**.

1. **Optional:** Copy the `demo` folder for testing (located in `QZ Tray\demo`) from QZ Tray to the client.

1. Optionally, if Firefox support is needed, enable the [Enterprise Roots policy](https://support.mozilla.org/en-US/kb/setting-certificate-authorities-firefox)


   > **Note:** If unsure of the location of these files on your system install QZ Tray on a test system and search for them by name.  They are both created by QZ Tray and can safely be overwritten.

The rest of the steps must be completed on the client

### Certificate Installation

Certificate installation varies between browsers and platforms.  Distributing a certificate marked for a custom hostname is a manual process which must be done on all workstations requiring HTTPS + QZ Tray.

   #### Windows 
   1. Double click `root-ca.crt`
   1. Click Install Certificate
   1. Local Machine, Next
   1. Choose "Trust Root Certificate Authorities" and hit Next

      ![image](https://cloud.githubusercontent.com/assets/12505463/11509674/7eb86174-982d-11e5-9519-27ec456cc02c.png)
  
   1. Click Finish.  If successful, the following screen will appear:

      ![image](https://cloud.githubusercontent.com/assets/12505463/11509765/0ef39b78-982e-11e5-9f68-39425a1624b0.png)

   #### Linux

   1. Run the following command to install the certificate

      ```bash
      certutil -d sql:$HOME/.pki/nssdb -A -t TC -n  "QZ Industries, LLC" -i /path/to/root-ca.crt
      ```

   #### macOS
   1. Double click `root-ca.crt`
   1. Choose System, Add

      ![image](https://cloud.githubusercontent.com/assets/12505463/13585469/f727f34c-e489-11e5-8342-6d9e6ae71996.png)

   1. From KeyChain set certificate to "Always Trust"

   #### iOS (iPad, iPhone)

   1. As of 2.0.9 `root-ca.crt` is automatically hosted on the about status page of QZ Tray
      * To access the about page, navigate (in Safari) to the printer server on port 8182 (e.g. http://foobar:8182/)<br><br>:warning: **Warning:** A bug with 2.1.0 prevents the `root-ca` Download from working properly on iOS and manual installation (e.g. email) is needed instead.  This is fixed in 2.1.1.

      * Under the SSL section, click the "Download certificate" with the alias of the newly created certificate.

   1. If you are not running QZ Tray 2.0.9 +, you will need to host the `root-ca.crt` somewhere so you can download it from the web (e.g. temporarily on a webpage, Google drive, etc.). The about page will not have the certificate to download.

   1. Use Safari to download the `root-ca.crt`. Safari will automatically prompt the installation of the certificate. Chrome and other browsers will not. When Prompted, select "Allow"

      ![image](https://user-images.githubusercontent.com/12505463/51137124-21f6b000-180c-11e9-9d94-0d931c36c089.png)

   1. The profile installer screen should automatically appear. Select "Install"

      ![image](https://user-images.githubusercontent.com/12505463/51137472-ad704100-180c-11e9-9ad3-2b87f4ed16a3.png)

   1. Next, go to **Settings > General > About > Certificate Trust Settings**. The newly installed certificate should appear. Toggle to enable full trust for root certificates

      ![image](https://user-images.githubusercontent.com/12505463/51137680-1ce63080-180d-11e9-90c6-f9e186d3b5c6.png)

  
   #### Android using Chrome browser (Pixel, Moto, Samsung, et cetera smart devices)
    
* As of the publication of this section of the tutorial (April 2, 2021), 
Android must have a server-side modification for the IP address of the local print-server:

```bash
qz.websocket.connect({ host: 'IPv4.ADDRESS.OF.LAN-PRINT-SERVER', usingSecure: false });

```
* Next, the client (tablet, smart phone) must make an exception in Chrome to allow insecure traffic from this particular address.
* Open Chrome on the smart device, and navigate to:

`chrome://flags/#unsafely-treat-insecure-origin-as-secure`

* From here, ENABLE the ability to use this feature, and add **ws://IPv4.ADDRESS.OF.LAN-PRINT-SERVER:8182** (8182 is our first default for ws://) to the exception list

   <img width="600px" src="https://user-images.githubusercontent.com/3408749/115759258-0c1eb080-a36e-11eb-9bf3-fa7feb9e0d25.png" alt="Chrome exception android"/>
* You should then be able to navigate to that page from your Android Chrome browser:

   <img width="600px" src="https://user-images.githubusercontent.com/3408749/115759196-fad5a400-a36d-11eb-87b0-82bb5e95f720.png" alt="About info on insecure"/>





### Import Firefox certificate

#### Windows, MacOS
* Install the certificate to the OS per above instructions
* Navigate to `about:config`
* Search for `security.enterprise_roots.enabled`, change the value to `true`.

#### Linux
* Use the `Certificates > Install` directive using `policies.json` as described here: https://github.com/mozilla/policy-templates/blob/master/README.md#policiesjson-9

<details>
   <summary>Legacy Firefox Versions</summary>

   Firefox does not allow self-signed certificates to be imported directly, instead a manual import process is needed.

   1. Overlay the existing Firefox files with the ones transferred over from the server.

      ```
      defaults/pref/firefox-prefs.js
      firefox-config.cfg
      ```

   1. Again, these may be located in `C:\Program Files (x86)\Mozilla Firefox\`
   </details>

### Verify Secure Connection

1. Open `sample.html` in a browser
1. Right Click, Inspect Element on the sample page
1. Click the Console tab
1. Verify the secure websocket connection to 8181 succeeds.  If a connection is made on a different port, reference the table below

   |Secure (HTTPS)| Insecure (HTTP)|
   |--------------|----------------|
   |8181          | 8182           |
   |8282          | 8283           |
   |8383          | 8384           |
   |8484          | 8485           |

   > **Note:** If HTTPS still fails, or you are experiencing long timeouts before a connection is made, a firewall may be blocking the connection.  Try disabling the firewall, or allowing the port on the server.

   ![image](https://cloud.githubusercontent.com/assets/12505463/11510017/78b437ba-982f-11e5-904a-b2f7779ccb8a.png)
