### Compatibility

* :white_check_mark: 2.1 | :white_check_mark: 2.0 | :white_check_mark: 1.9 | [...](Roadmap)

***

1. General Questions
   * [How does the certificate licensing work?](#licensing)
   * [How do you suppress print warning dialogues?](#suppressing-warnings)
   * [How much does QZ Tray cost?](#qz-tray-cost)
   * [Can a new version be installed on top of an old version?](#installing-new-version)
   * [Does QZ Tray make changes to the browser?](#browser-managed-by-organization)
1. Minimum Requirements
   * [Java](#java-versions)
   * [OS](#operating-systems)
   * [Browser](#web-browsers)
1. Known Issues
   * [What apps might conflict with QZ Tray?](#conflicting-applications)
1. Troubleshooting
   * [Sorry, this version of QZ Tray cannot be installed on this system](#installation-issues)
   * [How do I debug QZ Tray?](#debugging-qz-tray)
   * [Why does QZ Tray keep crashing?](#crash-during-printing) 
   * [Why won't QZ Tray load?](#cannot-launch-software) 
   * [Why can't I list my printers?](#cannot-list-printers)
   * [Why is my thermal printer only printing text characters?](#raw-printer-queues)
   * [How do I debug WebSockets?](#debugging-websockets)
   * [Why do I receive invalid JSON?](#invalid-json)
   * [Why do I receive `Automatic certificate installation is not available for this platform`](#jscript) 
   * [Why do I receive `Can’t find script engine “JScript” for script.`](#jscript)
   * [Why does the C#/VB/.NET code signing not work in Azure?](#azure-fingerprint-setting)
   * [Why is my code signing not working with CloudFront?](#cloudfront-cache-settings)
   * [Why do I receive `Keyset does not exist` when using `sign-message.cs` or `sign-message.vb`?](#keyset-does-not-exist)

***

### General

#### Licensing
 * When you [purchase Premium Support](http://buy.qz.io/) you are granted the ability to generate a digital certificate and a public/private key pair to suppress all warning dialogues.  This certificate has:

  * No client/user limitations
  * No domain restrictions

    *A single license will suffice for 99% of organizations*

 * The purchase of the premium version also provides technical support for one year.  The Technical Support Team of QZ Industries, LLC will work diligently to help integrate the software into your environment.  All questions are promised to be answered as fast as possible (usually well within a 24-hour period).


#### Suppressing Warnings
 * In order to suppress print warning dialogues, all print requests must be signed using the Intermediate Certificate and private key.  An in-depth tutorial explaining how to do this can be [found here](signing).

#### QZ Tray Cost
 * QZ Tray is free.  Organizations wishing to print silently are required to purchase a certificate from QZ Industries, LLC or implement their [own root certificate](signing#to-override-the-trusted-root-certificate).  Pricing options can be found on QZ's homepage at https://qz.io.

#### Installing New Version
 * Installation of a new version will succeed even if an older version is present and/or running.
 * **Note:**  QZ Tray 2.0 is not backwards compatible with QZ tray 1.9.

#### Browser Managed By Organization

Question: Does QZ Tray make changes to the browser?

Yes.  For normal QZ Tray operations a few, small changes are needed, please see below:

* QZ Tray makes the following browser changes for proper operating:
   * **Google Chrome:**<br>A policy file is installed to the system to allow `qz:launch` URLs to open without prompting the user
      * Windows: `HKLM\SOFTWARE\Policies\Google\Chrome\URLWhitelist`
      * MacOS: `/Library/Preferences/com.google.Chrome.plist`
      * Linux: `/etc/opt/chrome/policies/managed`
   * **Firefox:**<br>A policy file is installed to the system to allow Firefox to use the computer's certificate store.
      * All Operating Systems: `<FIREFOX INSTALL>/distribution/policies.json`
      * ESR Firefox Versions: `<FIREFOX INSTALL>/firefox-config.cfg`

***

### Minimum System Requirements

#### Java Versions
   
| Product           | Minimum Version             |
| ------------------|-----------------------------|
| QZ Tray 2.2          | OpenJDK 11 (bundled)        |
| QZ Tray 2.1          | Java 8 / OpenJDK 1.8*       |
| QZ Tray 2.0          | Java 7 / OpenJDK 1.7*       |
| QZ Tray 1.9          | Java 7 / OpenJDK 1.7        |
| QZ Print 1.9         | Java 6 / OpenJDK 1.6        |

\*HTML printing in QZ Tray 2.x requires Java 8 + Java FX.<br>

#### Operating Systems

| Operating System  | Minimum Version                |
| ------------------|--------------------------------|
| Apple OS X        | OS X 10.7 Lion or higher*      |
| Microsoft Windows | Windows XP or higher*          |
| Linux             | Ubuntu 12.04 Precise or higher |

> Version 2.0 or higher requires `512MB` of free memory (RAM), recommends `6GB` or higher total system memory for raster graphics.

\*Safari 5.1.7 is bundled with Lion but is not supported.  Alternate browser required.<br>
\*Windows XP requires manual certificate installation. Will prompt at install time.

#### Web Browsers

| Web Browser       | Minimum Version             |
|-------------------|-----------------------------|
| Safari            | Safari 6.0.3 or higher      |
| Firefox           | Firefox 31 or higher*       |
| Chrome            | Chrome 31 or higher         |
| Internet Explorer | IE 10 or higher*            |
| Microsoft Edge    | 20.x or higher*             |

\* Firefox requires a [browser restart for HTTPS](using-qz-tray#firefox-steps)<br>
\* IE/Edge on domain networks require external DNS access to `localhost.qz.io` due to a [Local Intranet Limitation](https://github.com/qzind/qz-print/issues/39#issue-87042860).

***

### Known Issues

#### Conflicting Applications
The following applications may cause issues with QZ Tray installation.
 * **Older Java versions:**  QZ Tray works best with Java 11 or higher.  Older QZ Tray versions may conflict with QZ Tray and cause it to fail to start.
   * If possible, uninstall all versions of Java, JRE, JDK or OpenJDK from the system.
   * If uninstalling old versions of Java is not possible, install QZ Tray 2.2 or higher, which will come bundled with its own Java version.

 * **Antivirus Software:** Some antivirus applications may prevent QZ Tray from installing or running.
   * Comodo Antivirus will force application containment the QZ Tray installer and prevent it from being installed, uninstalled and prevent it from running.  Moreover, disabling Comodo will have very little effect because it will re-enable itself without warning.  QZ Tray is not supported with Comodo Antivirus.
 * **Other Antivirus:**
   * **Application containment:** If the antivirus forces application containment, please disable this application containment for QZ Tray and the Java Runtime which it uses.
   * **Web Filtering:** If the antivirus blocks access to local ports or localhost certificates, please disable or whitelist `https://localhost:8181`, `http://localhost:8182`.
   * **Temporary File Access:** If the antivirus blocks access to the temporary directory, please disable or whitelist the QZ Tray installer.
   * **Other issues:** QZ Industries does not provide support for issues created by third party antivirus applications.  Built-in features (such as Windows Defender) are supported and may be escalated to the various [support](https://qz.io/support/) channels.

### Troubleshooting

#### Installation Issues
 * If QZ Tray fails to install with the error "Sorry, this version of QZ Tray cannot be installed on this system"
    * This could be caused by mismatched architecture.  QZ Tray is only supported on 64-bit Intel and 64-bit ARM/Apple Silicon.  Please ensure the version you are installing matches your architecture. 
    * This could be caused on Windows if the username has not ANSI-characters in the username.  For example, `Sørina` may cause the installer to fail, whereas `Sorina` will succeed.  This is a limitation of Java, but a workaround is available in [issue #1095](https://github.com/qzind/tray/issues/1095#issuecomment-**********).
    * This could be caused on Windows if the username has an exclamation mark in it.   For example, `Lenovo!` may cause the installer to fail, whereas `Lenovo` will succeed.  This is a limitation of Java.  No workaround is available, the software must be installed using a different user account without the exclamation mark.
    * This could be caused on a computer running a 32-bit operating system.  No workaround is available.  Older 2.1 versions may work with 32-bit Java runtimes, but this configuration is not officially supported.

#### Debugging QZ Tray

##### Logs
 * To automatically retrieve the logs:
   * From the QZ Tray Icon:<br>**Advanced, Troubleshooting, Zip Logs (To Desktop)**<br>(They will be placed on the `Desktop` in a ZIP file)<br><br>
 * To manually retrieve the logs:<br>QZ Tray places log files in the following locations:
   * Windows:  `%APPDATA%\qz`
   * Mac: `~/Library/Application Support/qz`
   * Linux: `~/.qz`

##### Console Logs
 * The recommended approach for getting verbose output from QZ Tray is to launch the software from command line.  This will expose the Java Console with detailed information about debug messages and exceptions including full backtrace of where a particular exception occurred.

 * QZ Tray launches with `512MB` of memory.  We must specify `-Xms512M` in the command in order to prevent unexpected behavior and crashing. This unnecessary for Mac unless following the basic syntax (`java -jar qz-tray.jar`) . More memory can be allocated but should not be necessary.

   **Windows** (after closing all instances of QZ Tray)

   ```cmd
   "%PROGRAMFILES%\QZ Tray\qz-tray-console.exe"
   REM or for 2.1 and older: java -Xms512M --add-exports java.desktop/sun.swing=ALL-UNNAMED -jar "%PROGRAMFILES%\QZ Tray\qz-tray.jar"
   ```

   **Mac OS X** (after closing all instances of QZ Tray)

   ```bash
   /Applications/QZ\ Tray.app/Contents/MacOS/QZ\ Tray
   ```

   **Linux/Other** (after closing all instances of QZ Tray)

   ```bash
   /opt/qz-tray/qz-tray
   ```

##### Proxy configuration
Organizations utilizing a proxy for network access may experience connectivity issues with QZ Tray.
   * QZ recommends adding proxy exceptions to network resources required by QZ Tray
   * Alternatively, QZ Tray's Java instance may be coerced to use the system proxy by setting `java.net.useSystemProxies=true` in `<APP DIR>/runtime/conf/net.properties`.

##### PKIX Build Path Failed

Sometimes a message `java.lang.UnsupportedOperationException: Cannot parse (FILE)` will appear.  This is often a failure by Java to download the specific resource and it will be followed by `Caused by: javax.net.ssl.SSLHandshakeException: sun.security.validator.ValidatorException: PKIX path building failed`

   * To fetch detailed HTTPS/SSL certificate information, append `-verbose -Djava.security.debug=certpath` before the `-Xms512` command.  The output will be very large, you may redirect it to file using `> file.log 2>&1` at the end.
   * See also https://github.com/qzind/tray/files/2194181/Document.1906321.1.pdf
   * **Common Causes**
     * **Broken certificate chain** Remedy: Check validity sslshopper.com, fix chain per CA instructions.
     * **Self-signed certificate**  Remedy: Add an exception to Proxy or Firewall for this address -- OR -- Install certificate into Java.
     * **Newly-trusted CA**  Remedy: Update Java.  This is fairly common for LetsEncrypt certificates.
   * Possible remedy: Add `-Djavax.net.ssl.trustStoreType=WINDOWS-ROOT` to the [command line options](command-line#contents).

#### Crash During Printing
#### Cannot Launch Software
 1. A corrupt Java installation may prevent the software from installing or loading.  A corrupt Java installation is fairly common in Windows, and can be resolved by reinstalling Java using the Java [offline installer](https://java.com/en/download/manual.jsp).  The offline installer has been reported to fix many issues with corrupt Java installations.  Take a look at [this guide](upgrading-java) for our recommended steps on reinstalling Java on all platforms. <br><br>**Signs of a corrupt Java install:**

    > `Creating keystore for ws://localhost...`<br>` - [failed] Creating a CA keypair...`<br><br>... or ...<br><br>
    > **`java -version`**<br>`"An error has occurred while processing the shared archive file. Unable to unmap shared space. Error occurred during initialization of VM. Unable to use shared archive."`

 1. Sometimes custom `JAVA_HOME` environmental variables will prevent QZ Tray from loading.  If the software can be launched [via the command line](#debugging-qz-tray) this is likely the case. In windows this can be checked by going to **System > Advanced system settings > Environment Variables**

 1. HTML printing (JavaFX) can cause printing to fail if the following is true:
    * **Issue**: HTML page width is set to 100%.<br>**Remedy:** Hard-code an acceptable page with using CSS, e.g. `800px`
    * **Issue**: Printer resolution for HTML print is high (e.g. 600dpi, 1200dpi).<br>**Remedy**: Hard-code a lower DPI (e.g. 300dpi) using [`{ units: 'in', density: 300 }`](https://qz.io/api/qz.configs#.setDefaults).  We also recommend setting a sane `fallbackDensity` incase 300dpi is unsupported by certain hardware.
    * **Issue**: Out of memory issues can occur if running the 32-bit JVM.<br>**Remedy**:  See **32-bit vs. 64-bit:** section below.
    * **Issue**: `java.lang.NoClassDefFoundError: javafx/print/PrintColor at qz.printer.PrintOptions$ColorType`<br>**Remedy**: JavaFX is not distributed with all Java versions.  Please install JavaFX for your platform (e.g. `openjfx`, etc).

 1. **Images/PDFs cause software to stop working**: More commonly `Cannot call method public void qz.ws.PrintSocketClient#onMessage`, this is generally a sign that the JVM has hit an unrecoverable error.  The most common cause is large print operations on a 32-bit JVM.  A temporary workaround is to specify [`{ rasterize: false }`](https://qz.io/wiki/2.0-Pixel-Printing#rasterize) in the configuration, but this will only fix PDFs.  A permanent solution is to switch to a 64-bit JVM which allocates a larger heap space.  If 32-bit is required, additional heap space may be allocated via [command-line](#troubleshooting).

 1. **32-bit vs. 64-bit:** Some 64-bit workstations have both 32-bit and 64-bit Java versions installed.  If both are needed, make sure both are upgraded, otherwise, removing the 32-bit version is advised to assist with the troubleshooting process.

 1. **Insufficient Java Version** `"A Java Exception has occurred"` may appear if attempting to launch against an outdated Java version.  For a list of minimum Java requirements see [Java Versions](#java-versions).  May also be reported as `Could not find the main class qz.ws.PrintSocketServer. Program will exit.`

 1. **Disk Corrupted:** Always try `fsck` (Linux), `Disk Utility` (MacOS) or `chkdsk` (Windows).  Physical drives can show strange symptoms when they are beginning to fail however SSDs hard drives are susceptible to a condition called "worn bits" or more properly "SSD Endurance" which is responsible for re-using NAND flash memory.  This process should be self-healing but on occasion can cause issues with certain drive firmwares.  Make sure to scan your disk for health when encountering launch issues.

#### Cannot List Printers

1. Mac

   Due to an upstream bug with Java, printer listings may fail on Mac.

   The current workaround is to:
   * Open System Preferences
   * Printers and Scanners
   * Right Click > Reset Printing System
   * Re-add all printers

   We realize this is not ideal, but have found no another work-around on Mac.

1. Windows

   Although rare, there are scenarios of not being able to list printers, only being able to list one printer, or listing printers takes several minutes to complete.  This is likely caused by a driver conflict between the offending printer driver and the print spooler, resulting in the entire print subsystem to hang.

   The workaround is to remove the offending printer or printer driver.

#### Raw Printer Queues

If your thermal or label printer has undesired results, it may be improperly configured. Here are our tutorials for setting up a printer to receive raw languages on each platform:
 * [Windows](https://qz.io/wiki/setting-up-a-raw-printer-in-windows)
 * [OS X](https://qz.io/wiki/setting-up-a-raw-printer-in-osx)
 * [Linux](https://qz.io/wiki/setting-up-a-raw-printer-in-ubuntu-linux)

**Note:** Some Windows drivers, such as Zebra's ZDesigner driver for Microsoft Windows, will support raw printing alongside pixel-based/PostScript printing. You may use those drivers or a raw-generic/text-only driver to send raw commands, but the generic driver will not be able to print images/PDF/HTML.

#### Debugging WebSockets

By default, QZ Tray suppresses verbose websocket information from the console, however occasionally this information may be useful for debugging/troubleshooting connectivity issues.

##### Debugging WebSockets in 2.x

* **2.x** - Using the [`qz.api.showDebug(...)`](https://qz.io/api/qz.api#.showDebug) API option, verbose connection details will logged to the browser's console.

   ```javascript
   qz.api.showDebug(true);  // must be called prior to calling qz.websocket.connect()
   ```

##### Debugging WebSockets in 1.9

* **1.9 Only** - Using the [`deployQZ(...)`](https://github.com/qzind/qz-print/blob/1.9/js/qz-websocket.js#L50) API option, verbose connection details will logged to the browser's console.

   ```javascript
    deployQZ(null, true);  // usage: deployQZ(hostname, debug);
   ```

#### Socket Disconnected
* This is often a sign of an incompatible version.  Please make sure you are using a build correct for your system (e.g. 1.9, 2.0)

#### Other Errors
* Console logs say `Process finished with exit code 2`
   * Cause: This indicates another instance of QZ Tray is already running on the designated ports on that machine.  If it has become unresponsive, attempt to kill the QZ Tray's `java` process and try launching it again.
* Error: `typeerror: _qz.tools.hash is not a function` 
   * Cause: Make sure the page is being rendered with UTF-8 `<meta charset='utf-8'>`.
* Error: `WebSocket connection to 'wss://localhost:8181/' failed: Error in connection establishment: net::ERR_CONNECTION_CLOSED`
   * Cause:  Java 9 (Beta) is known to cause this error.  Please downgraded to a supported [Java version](#java-versions).
* Error: `SCRIPT1010: Expected identifier qz-tray.js`
   * Cause:  IE11 may be emulating an older browser.  Please use IE10, IE11 or Edge browser emulation.  Offending code may look like this `<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" />`
* Error: `Server returned HTTP response code: 403 for URL`
   * Cause: Assuming permission issues have been ruled out, some servers require a `User Agent String` to be passed to the web controller.  QZ Tray 2.0 sends a blank `User Agent String`.  QZ Tray 2.1+ provides a proper `User Agent String`.  Alternately, disable the `User Agent Filtering` on the web controller.  For example, Cloudfare callls this `Browser Integrity Check` and can be disabled.

#### Invalid JSON
* If the browser console says `Invalid JSON`, try changing your HTTP headers in the sign-message example to use `text/plain` instead of `application/json`.  In PHP, this is done via:

   ```diff
   - header('Content-Type: application/json');
   + header('Content-Type: text/plain');
   ```

  **Note:** This technique will vary depending on the server-side language being used.

***

#### JScript
* Error: `Automatic certificate installation is not available for this platform`
   * Cause: Several.
      * **Note:** [QZ Tray 2.1](https://qz.io/download) uses Java for certificate installation and may correct this problem.
      * If using Comodo antivirus, use Advanced View, Auto-containment, Disabled before installing.
      * If using Windows XP, follow the on-screen instructions.
      * This issue may be caused by bad data being returned from `certutil` command.  Contact <EMAIL> for assistance.
* Error: `Can’t find script engine “VBScript” for script.`
   * **Note:** [QZ Tray 2.1](https://qz.io/download) has removed the `VBScript/JScript` dependency and may correct this problem.
   * Cause: The Windows desktop installers for Windows use `JScript` engine for certain tasks.  On occasion Antivirus Engines may try to replace this engine.  To determine if this is the cause run the following commands from Command Prompt:
      ```bat
      reg query HKCR\CLSID\{cc5bbec3-db4a-4bed-828d-08d78ee3e1ed}\InprocServer32 /ve
      reg query HKCR\CLSID\{f414c260-6ac0-11cf-b6d1-00aa00bbbb58}\InprocServer32 /ve
      reg query HKCR\CLSID\{f414c261-6ac0-11cf-b6d1-00aa00bbbb58}\InprocServer32 /ve
      reg query HKCR\CLSID\{f414c262-6ac0-11cf-b6d1-00aa00bbbb58}\InprocServer32 /ve
      ```
   * If the system is configured properly, each command will return the following value:
      ```
      (Default)    REG_SZ    C:\Windows\System32\jscript.dll
      ```
   * If the system is misconfigured, it will return some other value.  This value can be repaired by hand using the Windows Registry Editor.

***

##### Azure Fingerprint Setting
1. Within the Azure web app, in the section **Settings**, **TLS/SSL Settings**, **Private Key Certificates**, **Import**
   * Upload the `.pfx` and type the password.
2. Within the web app, in the **Settings**, **Configuration**, **Application Settings**, **New Application Setting**
   ```cmd
   WEBSITE_LOAD_CERTIFICATES          *
   ```
3. Adjust the code, replacing "`<fingerprint>`" with the certificate fingerprint.
   ```diff
   -	var KEY = "private-key.pfx";
   -	var PASS = "S3cur3P@ssw0rd";
   -
   -	var cert = new X509Certificate2( KEY, PASS, X509KeyStorageFlags.MachineKeySet | X509KeyStorageFlags.PersistKeySet | X509KeyStorageFlags.Exportable );

   +	X509Store certStore = new X509Store(StoreName.My, StoreLocation.CurrentUser);
   +	certStore.Open(OpenFlags.ReadOnly);
   +	X509Certificate2Collection certs= certStore.Certificates.Find(X509FindType.FindByThumbprint, "<fingerprint >", false);
   +	// Get the first cert with the fingerprint
   +	if (certs.Count > 0)
   +	{
   		X509Certificate2 cert = certs[0];
   		
   		RSACryptoServiceProvider cspStrong = (RSACryptoServiceProvider)cert.PrivateKey;
   
   		byte[] data = new ASCIIEncoding().GetBytes(request);
   		byte[] hash = new SHA1Managed().ComputeHash(data);
   		Response.ContentType = "text/plain";
   		Response.Write(Convert.ToBase64String(cspStrong.SignHash(hash, CryptoConfig.MapNameToOID("SHA512"))));
   +      }
   ```

### Cloudfront Cache Settings
* In some instances, Amazon CloudFront will cache the query-strings sent to a signing controller. The symptom of this is intermittent popups as some signing requests will show "invalid" signature, others will show as valid.
* To fix this, you will need to modify the CloudFront Cache Behavior:
   * Edit Cache Behavior, Cache Key Contents, Query Strings, Change from "None" to "All"

#### Keyset Does Not Exist

If the .NET signing example fails on a load balanced server, please try one of the following:

##### IIS User Profile Setting
1.  Change the setting which toggles the user profile for the application pool identity user. When set to false, the key containers aren't accessible:
   * IIS, Application Pools, Advanced Settings
   * Set Load User Profile option to True
   * See also https://stackoverflow.com/a/10048789/3196753