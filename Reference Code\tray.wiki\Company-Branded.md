### Compatibility
* ✅ 2.2 | ✅ 2.1 | ✅ 2.0 | ⛔️ 1.9 | [...](Roadmap)

### Objective
* Create a custom build with your company's logo and information (also known as "white labeling").

### Prerequisites 
* Purchase [Company Branded + Premium Support](https://buy.qz.io/)
  * Company Branded + Premium Support is not included in standard support. Please [purchase here.](https://buy.qz.io/)

* Artwork
  * SVG [desktop icon](#build-qz-tray)
  * PNG [about banner](#build-qz-tray)
  * PNG [green, yellow, red and mask tray icons](#build-qz-tray)

### Steps

#### Login
Log in to the Company Branded section of the website


1. Navigate to https://qz.io/login/
1. Enter in the product key and click **Sign In**
1. Once logged in, select **Company Branded**

#### Build QZ Tray

1. Fill in your company's information.  These fields will be used to replace all references to "QZ" for use in files, directories, and the GUI.

   |Field            |Example     |Description  |                                                                    
   |-----------------|-------------|----------------------------------------------------------------------------------|
   |Application Name         |QZ Tray |Main application name appears Control Panel, desktop shortcuts, etc.|
   |Application Abbreviation |qz-tray |Abbreviated version of your application name (qz-tray.exe, qz-tray.jar, etc.) |
   |Vendor Abbreviation      |qz      |Short name used for the local data directory (`%APPDATA%\qz`) |
   |Company Name        |QZ Industries, LLC |Full company name for security dialogues, About Dialogues, and SSL Certificate |
   |Website URL              |https://qz.io |Company website, shows in the About Dialogue |
   |Support Email       |<EMAIL>   |email address that appears in the Control Panel, About Dialogue, SSL Certificate |
   |City                     |Canastota     |City of business |
   |State/Province           |NY            |State/province of business |
   |Country                  |US            |Country of business |

1. Provide your brand's images. You will need to upload 5 images for the build process.

   |Image                 |Format      |Example |Description |
   |----------------------|------------|--------|------------|
   |Desktop Icon          |SVG         |![image](https://cloud.githubusercontent.com/assets/12505463/22177361/0a8be446-dfea-11e6-99f3-4a7aa94b2e80.png) |Used for the desktop shortcut |
   |System Tray - Mask | PNG           |![mask](https://user-images.githubusercontent.com/6345473/75050947-fb895680-549a-11ea-8738-e2a3bd0cec4c.png) `(48px x 48px)` | Must be black and transparent only.  Used for system tray icon on modern desktops |
   |System Tray - Default |PNG         |![default](https://user-images.githubusercontent.com/6345473/75050978-0b089f80-549b-11ea-9f31-c3bd95bc6271.png) `(48px x 48px)` |Used for the system tray icon on older desktops |
   |System Tray - Warning |PNG         |![warning](https://user-images.githubusercontent.com/6345473/75051002-16f46180-549b-11ea-8f45-1cbc4d442f4a.png) `(48px x 48px)` |Shows in tray when the software is loading |
   |System Tray - Danger  |PNG         |![danger](https://user-images.githubusercontent.com/6345473/75051025-207dc980-549b-11ea-82a7-725b9f9cc895.png) `(48px x 48px)` |Shows in tray when the software is in an error state |
   |About Dialog Banner |PNG         |![about](https://user-images.githubusercontent.com/6345473/75051306-a1d55c00-549b-11ea-8562-8a107b67fe0f.png) |Shows in the About Dialog

1. Provide your brand's color.  This color will be applied to buttons and menu icons.
   <br><img width="253" alt="image" src="https://user-images.githubusercontent.com/6345473/188791676-3f14c731-b618-4e92-ad36-a907affce23b.png">

1. Provide any custom [provisioning](https://qz.io/docs/provisioning) options.  Provisioning options may include:
   * Automatic trusting of [signing certificate](https://qz.io/docs/signing) ("Remember this decision")
   * Automatic uninstall "QZ Tray" branded versions
   * Custom Websocket Ports (e.g. change port `8181`)
   * Additional [provisioning features](https://qz.io/docs/provisioning) will be added [as-needed/requested](https://qz.io/contact/).


1. Request your build.

 * **Optional:** input your email address if you would like an alert when the build is complete.
 * Click "Run Build" - this process takes around 5 minutes to complete
   * This will create three installers, one for Windows(`*.exe`), Mac(`*.pkg`), and Linux(`*.run`)
    * All builds will be stored in this portal and can be downloaded at any time

       <img width="726" alt="image" src="https://user-images.githubusercontent.com/6345473/188791830-9bc63a1d-20d3-492b-a97a-8dd7d04d50eb.png">