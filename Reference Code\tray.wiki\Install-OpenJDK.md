### Compatibility
* :white_check_mark: 2.1 | :white_check_mark: 2.0 | :white_check_mark: 1.9 | [...](Roadmap)

### Objective

Quickly install OpenJDK on Mac, Windows, Linux.

### Linux

1. Use the `openjdk8` or `openjdk11` package provided by your Linux distribution.
   ```bash
   # Example
   sudo apt-get install openjdk11-jdk
   ```

### Mac

1. Download OpenJDK8 or OpenJDK11 Hotspot VM from https://adoptopenjdk.net/.
1. From Terminal:
   ```bash
   sudo mkdir -p /Library/Java/JavaVirtualMachines/
   sudo tar -xf ~/Downloads/openjdk-*.tar.gz -C /Library/Java/JavaVirtualMachines/
   ```

### Windows

1. Download OpenJDK8 or OpenJDK11 Hotspot VM from https://adoptopenjdk.net/.
1. From PowerShell
   ```ps1
   powershell Start-Process powershell -Verb runAs
   Expand-Archive -Path "$env:USERPROFILE\Downloads\OpenJDK*.zip" -DestinationPath "$env:PROGRAMFILES\OpenJDK\"
   
   #Environmental vars
   $env:JAVA_HOME = (Get-ChildItem -Path "$env:PROGRAMFILES\OpenJDK" | Where-Object {$_.PSIsContainer} |
       Sort-Object LastWriteTime -Descending | Select-Object -First 1).FullName
   $env:PATH += ";$env:JAVA_HOME\bin"
   setx /M JAVA_HOME "$env:JAVA_HOME"
   setx /M PATH "$env:PATH"
   
   # Registry
   $VERSION = ("$env:JAVA_HOME" -Split "\\jdk-")[1]
   $MAJOR_VERSION = ($VERSION -Split "\.")[0] + "." + ($VERSION -Split "\.")[1]
   reg add "HKLM\SOFTWARE\JavaSoft\JDK" /v CurrentVersion /t REG_SZ /d "$MAJOR_VERSION" /f
   reg add "HKLM\SOFTWARE\JavaSoft\JDK\$MAJOR_VERSION" /v JavaHome /t REG_SZ /d "$env:JAVA_HOME" /f

   # Cleanup
   Remove-Item -Path "$env:USERPROFILE\Downloads\OpenJDK*.zip"
   ```
