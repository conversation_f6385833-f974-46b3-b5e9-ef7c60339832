### Compatibility
* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :no_entry: 2.0 | :no_entry: 1.9 | [...](Roadmap)

### Objective
* Use [`sift` library](https://github.com/qzind/sift) to filter/block unwanted MAC addresses, network adapters, and printers.

### Prerequisites
* Must be using QZ Tray 2.1 or later
* Download and include [`sift.js`](https://github.com/qzind/sift)
  > **Note:** Optionally, you may [`npm install qz-sift`](https://www.npmjs.com/package/qz-sift)

  ```js
  <script type="text/javascript" src="path/to/sift.js"></script>
  ```

### Contents
* [Printer Details](printer-details)
* [Basic Syntax](#basic-syntax)
* Identify/Filter
   * [Printers](#printers)
   * [MAC Addresses](#mac-address-filtering)
   * [Scales](#scales)

### Printer Details

**Note:** The `sift.js` library is not needed for this.

You can get various printer information (driver, densities, default printer, trays) for system attached printers

```js
qz.printers.details();
```
The result is returned as an object array

#### Loop through your results

```js
function detailPrinters() {
    qz.printers.details().then(function(data) {
        for(var i = 0; i < data.length; i++) {
            console.log(data[i].name + " " + data[i].driver + " " + data[i].density + " " + data[i].trays);
        }
    }).catch(function(e) { console.error(e); });
}
```

### Basic Syntax

Remove whatever matches the `option` from the `data` object

 ```js
 sift.toss(data, { option });
 ```

Keep whatever matches the `option` criteria from the `data` object

 ```js
 sift.keep(data, { option });
 ```

### Printers

Per the [`sift` library](https://github.com/qzind/sift/#printer-prerequisites):
 * Printers must be supplied in an object array and must contain a printer `name` and printer `driver`.

   ```js
   [ { name: 'foo', driver: 'bar' }, { ... } ]
   ```

QZ Tray has a built-in function `qz.printers.details();` that meets this criteria

#### Virtual Printers

The sift library can be used to filter out virtual printers.  This can be useful for preventing a user from printing, for example, coupons to a PDF printer.

* Return only the physical printers

 ```js
function detailPrinters() {
    qz.printers.details().then(function (data) {
        data = sift.keep(data, { physical: true }); //same as sift.toss(data, { physical: false });
        console.log(data);
    }).catch(function(e) { console.error(e); });
}
 ```

* Return only the virtual printers

 ```js
function detailPrinters() {
    qz.printers.details().then(function (data) {
        data = sift.keep(data, { physical: false }); //same as sift.toss(data, { physical: true });
        console.log(data);
    }).catch(function(e) { console.error(e); });
}
  ```

#### Raw Printers

Return only raw printers

```js
function detailPrinters() {
    qz.printers.details().then(function (data) {
        data = sift.keep(data, { type: 'raw' });
        console.log(data);
    }).catch(function(e) { console.error(e); });
}
```

### Network Information

#### MAC Address Filtering

|Physical Adapter         |Virtual Adapter                      |
|-------------------------|-------------------------------------|
|`burnedIn: true`         |`burnedIn: false`                    |


Return only physical adapters

 ```js
function listNetworkDevices() {
    qz.networking.devices().then(function(data) {
       data = sift.keep(data, {burnedIn: true });
       console.log(data);
    }).catch(function(e) { console.error(e); });
 }
```

### Scales