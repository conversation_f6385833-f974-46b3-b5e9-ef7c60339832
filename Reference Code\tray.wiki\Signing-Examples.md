*  :white_check_mark: 2.2 | :white_check_mark: 2.1 | :white_check_mark: 2.0 | :no_entry: 1.9 | [...](Roadmap)

## Background
This page will walk you through setting up message signing in either PHP or JavaScript using the `demo` folder that is bundled with the QZ Tray client.

If you'd rather watch a video explaining this, we have that too!
* Video tutorials 🎥 :
    * [C# Signing Tutorial (YouTube)](https://youtu.be/u3XcvAEWvEI)
    * [PHP Signing Tutorial (YouTube)](https://youtu.be/OalEU2mZdEw)
    * [JavaScript Signing Tutorial (YouTube)](https://youtu.be/X72y6ZZTWzE)

## Outline
1. [Generate Signing Keys](#generate-signing-keys)
1. [Setup the Sample Environment](#setup-the-sample-environment)
1. [Option 1 - Server Side Signing (PHP)](#php)
1. [Option 2 - Client Side Signing (JavaScript)](#javascript)

## Generate Signing Keys
In order to remove the dialogue warnings, a certificate and private trusted by the root certificate in QZ Tray must be generated. "Premium Support" and "Company Branded" customers may generate a `digital-certificate.txt` and a `private-key` in `pem` and `pfx` formats [through our portal](https://qz.io/wiki/generate-certificate)

* [Reference this guide to generate your signing keys](generate-certificate)

## Setup the Sample Environment
For demonstration purposes, QZ Tray ships with `sample.html` for a complete demo of the product and features. We will be using this as a baseline for this tutorial.

1. Copy the `demo` folder that's installed with the client to your webserver
   * Windows: `C:\Program Files\QZ Tray\demo`
   * MacOS: `/Applications/QZ Tray.app/demo`
      * 2.2 and newer: `/Applications/QZ\ Tray.app/Contents/Resources/demo`
   * Linux: `/opt/qz-tray/demo`

1. Copy the keys you generated from the previous step to the demo folder on your webserver.
<img width="517" alt="image" src="https://user-images.githubusercontent.com/12505463/*********-5bed0603-39b8-4f1d-bb3c-855ce68236ad.png">

## Option 1 - Server Side Signing
This is the preferred configuration and is what the vast majority of clients will use. If a pure client side setup is mandatory, go to [Option 2 - Client Side Signing](#option-2---client-side-signing)

### PHP
### Edit sign-message.php
In the folder `demo\assets\signing` you will find examples for various languages. For this demo, we will be using PHP (sign-message.php)

1. Edit the `assets\signing\sign-message.php` script
1. Edit the line `$KEY = 'private-key.pem';` to the path of the `private-key.pem` we moved to the webserver in the previous step
   ```diff
   - $KEY = 'private-key.pem';
   + $KEY = 'demo\private-key.pem';
   ```

### Edit the JavaScript Functions
#### setCertificatePromise
1. Edit the `sample.html` page in the root of the demo folder and find the `setCertificatePromise()` function
   * This function has the contents of a `digital-certificate.txt` pasted in for demo purposes. We are going to use `fetch` (AJAX) instead. 
1. Delete everything inside of this function until it looks like the below.
   * Make sure the path to your `digital-certficate.txt` is correct! You should be able to navigate to the URL through the browser and see the contents of this file.

   ```js
    qz.security.setCertificatePromise(function(resolve, reject) {
       fetch("demo/digital-certificate.txt", {cache: 'no-store', headers: {'Content-Type': 'text/plain'}})
          .then(function(data) { data.ok ? resolve(data.text()) : reject(data.text()); });
    });
   ```

#### setSignaturePromise
1. Edit the `sample.html` page in the root of the demo folder and find the `setSignaturePromise()` function.
   * The demo is set to `resolve();` without receiving a signature (therefore there are warning dialogues). We need to point this function to the PHP controller we created earlier.
1. Edit this function until it looks like the below.
   * Make sure the URL in the `fetch()` is correct!

```js
   qz.security.setSignatureAlgorithm("SHA512"); // Since 2.1
   qz.security.setSignaturePromise(function(toSign) {
      return function(resolve, reject) {
         fetch("/demo/assets/signing/sign-message.php?request=" + toSign, {cache: 'no-store', headers: {'Content-Type': 'text/plain'}})
            .then(function(data) { data.ok ? resolve(data.text()) : reject(data.text()); });
        };
    });
```

That's it!

## Option 2 - Client Side Signing
As mentioned previously, this method is less preferred than server side signing. This method exposes the private key and signature to the client. Additionally, this method requires more work to maintain at renewal time.

### JavaScript
#### Edit the Certificate Function
1. Edit the `sample.html` page in the root of the demo folder and find the `setCertificatePromise()` function.
2. Paste the contents of your `digital-certificate.txt` directly in in the `reslove();`.
   * **Note:** You will need to edit the contents of the certificate to be in JavaScript format. The easiest way to convert this is to use a text editor with regex capabilities (Notepad ++, VSCode). **Find:** `\n`, **Replace:** `\\n" +\n"`

      <img width="565" alt="image" src="https://user-images.githubusercontent.com/12505463/*********-3c77d3e8-a38e-407d-ae66-2789dbfbece5.png">


      When complete, your entire function should look similar to this:

      ```js
      qz.security.setCertificatePromise(function(resolve, reject) {
          resolve("-----BEGIN CERTIFICATE-----\n" +
            "MIIFAzCCAuugAwIBAgICEAIwDQYJKoZIhvcNAQEFBQAwgZgxCzAJBgNVBAYTAlVT\n" +
            "MQswCQYDVQQIDAJOWTEbMBkGA1UECgwSUVogSW5kdXN0cmllcywgTExDMRswGQYD\n" +
            "VQQLDBJRWiBJbmR1c3RyaWVzLCBMTEMxGTAXBgNVBAMMEHF6aW5kdXN0cmllcy5j\n" +
            "b20xJzAlBgkqhkiG9w0BCQEWGHN1cHBvcnRAcXppbmR1c3RyaWVzLmNvbTAeFw0x\n" +
            "NTAzMTkwMjM4NDVaFw0yNTAzMTkwMjM4NDVaMHMxCzAJBgNVBAYTAkFBMRMwEQYD\n" +
            "VQQIDApTb21lIFN0YXRlMQ0wCwYDVQQKDAREZW1vMQ0wCwYDVQQLDAREZW1vMRIw\n" +
            "EAYDVQQDDAlsb2NhbGhvc3QxHTAbBgkqhkiG9w0BCQEWDnJvb3RAbG9jYWxob3N0\n" +
            "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtFzbBDRTDHHmlSVQLqjY\n" +
            "aoGax7ql3XgRGdhZlNEJPZDs5482ty34J4sI2ZK2yC8YkZ/x+WCSveUgDQIVJ8oK\n" +
            "D4jtAPxqHnfSr9RAbvB1GQoiYLxhfxEp/+zfB9dBKDTRZR2nJm/mMsavY2DnSzLp\n" +
            "t7PJOjt3BdtISRtGMRsWmRHRfy882msBxsYug22odnT1OdaJQ54bWJT5iJnceBV2\n" +
            "1oOqWSg5hU1MupZRxxHbzI61EpTLlxXJQ7YNSwwiDzjaxGrufxc4eZnzGQ1A8h1u\n" +
            "jTaG84S1MWvG7BfcPLW+sya+PkrQWMOCIgXrQnAsUgqQrgxQ8Ocq3G4X9UvBy5VR\n" +
            "CwIDAQABo3sweTAJBgNVHRMEAjAAMCwGCWCGSAGG+EIBDQQfFh1PcGVuU1NMIEdl\n" +
            "bmVyYXRlZCBDZXJ0aWZpY2F0ZTAdBgNVHQ4EFgQUpG420UhvfwAFMr+8vf3pJunQ\n" +
            "gH4wHwYDVR0jBBgwFoAUkKZQt4TUuepf8gWEE3hF6Kl1VFwwDQYJKoZIhvcNAQEF\n" +
            "BQADggIBAFXr6G1g7yYVHg6uGfh1nK2jhpKBAOA+OtZQLNHYlBgoAuRRNWdE9/v4\n" +
            "J/3Jeid2DAyihm2j92qsQJXkyxBgdTLG+ncILlRElXvG7IrOh3tq/TttdzLcMjaR\n" +
            "8w/AkVDLNL0z35shNXih2F9JlbNRGqbVhC7qZl+V1BITfx6mGc4ayke7C9Hm57X0\n" +
            "ak/NerAC/QXNs/bF17b+zsUt2ja5NVS8dDSC4JAkM1dD64Y26leYbPybB+FgOxFu\n" +
            "wou9gFxzwbdGLCGboi0lNLjEysHJBi90KjPUETbzMmoilHNJXw7egIo8yS5eq8RH\n" +
            "i2lS0GsQjYFMvplNVMATDXUPm9MKpCbZ7IlJ5eekhWqvErddcHbzCuUBkDZ7wX/j\n" +
            "unk/3DyXdTsSGuZk3/fLEsc4/YTujpAjVXiA1LCooQJ7SmNOpUa66TPz9O7Ufkng\n" +
            "+CoTSACmnlHdP7U9WLr5TYnmL9eoHwtb0hwENe1oFC5zClJoSX/7DRexSJfB7YBf\n" +
            "vn6JA2xy4C6PqximyCPisErNp85GUcZfo33Np1aywFv9H+a83rSUcV6kpE/jAZio\n" +
            "5qLpgIOisArj1HTM6goDWzKhLiR/AeG3IJvgbpr9Gr7uZmfFyQzUjvkJ9cybZRd+\n" +
            "G8azmpBBotmKsbtbAU/I/LVk8saeXznshOVVpDRYtVnjZeAneso7\n" +
            "-----END CERTIFICATE-----\n" +
            "--START INTERMEDIATE CERT--\n" +
            "-----BEGIN CERTIFICATE-----\n" +
            "MIIFEjCCA/qgAwIBAgICEAAwDQYJKoZIhvcNAQELBQAwgawxCzAJBgNVBAYTAlVT\n" +
            "MQswCQYDVQQIDAJOWTESMBAGA1UEBwwJQ2FuYXN0b3RhMRswGQYDVQQKDBJRWiBJ\n" +
            "bmR1c3RyaWVzLCBMTEMxGzAZBgNVBAsMElFaIEluZHVzdHJpZXMsIExMQzEZMBcG\n" +
            "A1UEAwwQcXppbmR1c3RyaWVzLmNvbTEnMCUGCSqGSIb3DQEJARYYc3VwcG9ydEBx\n" +
            "emluZHVzdHJpZXMuY29tMB4XDTE1MDMwMjAwNTAxOFoXDTM1MDMwMjAwNTAxOFow\n" +
            "gZgxCzAJBgNVBAYTAlVTMQswCQYDVQQIDAJOWTEbMBkGA1UECgwSUVogSW5kdXN0\n" +
            "cmllcywgTExDMRswGQYDVQQLDBJRWiBJbmR1c3RyaWVzLCBMTEMxGTAXBgNVBAMM\n" +
            "EHF6aW5kdXN0cmllcy5jb20xJzAlBgkqhkiG9w0BCQEWGHN1cHBvcnRAcXppbmR1\n" +
            "c3RyaWVzLmNvbTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBANTDgNLU\n" +
            "iohl/rQoZ2bTMHVEk1mA020LYhgfWjO0+GsLlbg5SvWVFWkv4ZgffuVRXLHrwz1H\n" +
            "YpMyo+Zh8ksJF9ssJWCwQGO5ciM6dmoryyB0VZHGY1blewdMuxieXP7Kr6XD3GRM\n" +
            "GAhEwTxjUzI3ksuRunX4IcnRXKYkg5pjs4nLEhXtIZWDLiXPUsyUAEq1U1qdL1AH\n" +
            "EtdK/L3zLATnhPB6ZiM+HzNG4aAPynSA38fpeeZ4R0tINMpFThwNgGUsxYKsP9kh\n" +
            "0gxGl8YHL6ZzC7BC8FXIB/0Wteng0+XLAVto56Pyxt7BdxtNVuVNNXgkCi9tMqVX\n" +
            "xOk3oIvODDt0UoQUZ/umUuoMuOLekYUpZVk4utCqXXlB4mVfS5/zWB6nVxFX8Io1\n" +
            "9FOiDLTwZVtBmzmeikzb6o1QLp9F2TAvlf8+DIGDOo0DpPQUtOUyLPCh5hBaDGFE\n" +
            "ZhE56qPCBiQIc4T2klWX/80C5NZnd/tJNxjyUyk7bjdDzhzT10CGRAsqxAnsjvMD\n" +
            "2KcMf3oXN4PNgyfpbfq2ipxJ1u777Gpbzyf0xoKwH9FYigmqfRH2N2pEdiYawKrX\n" +
            "6pyXzGM4cvQ5X1Yxf2x/+xdTLdVaLnZgwrdqwFYmDejGAldXlYDl3jbBHVM1v+uY\n" +
            "5ItGTjk+3vLrxmvGy5XFVG+8fF/xaVfo5TW5AgMBAAGjUDBOMB0GA1UdDgQWBBSQ\n" +
            "plC3hNS56l/yBYQTeEXoqXVUXDAfBgNVHSMEGDAWgBQDRcZNwPqOqQvagw9BpW0S\n" +
            "BkOpXjAMBgNVHRMEBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQAJIO8SiNr9jpLQ\n" +
            "eUsFUmbueoxyI5L+P5eV92ceVOJ2tAlBA13vzF1NWlpSlrMmQcVUE/K4D01qtr0k\n" +
            "gDs6LUHvj2XXLpyEogitbBgipkQpwCTJVfC9bWYBwEotC7Y8mVjjEV7uXAT71GKT\n" +
            "x8XlB9maf+BTZGgyoulA5pTYJ++7s/xX9gzSWCa+eXGcjguBtYYXaAjjAqFGRAvu\n" +
            "pz1yrDWcA6H94HeErJKUXBakS0Jm/V33JDuVXY+aZ8EQi2kV82aZbNdXll/R6iGw\n" +
            "2ur4rDErnHsiphBgZB71C5FD4cdfSONTsYxmPmyUb5T+KLUouxZ9B0Wh28ucc1Lp\n" +
            "rbO7BnjW\n" +
            "-----END CERTIFICATE-----\n");
       });
      ```

#### Edit the Signing Function
1. Go to `assets\signing\sign-message.js` and edit it.
1. Set the value of `privateKey` to the contents of `private-key.pem`. Just like with the certificate, you will want to use regex to properly format the string.
1. Go back to `sample.html` and include these before the `<body>` of the page.

   ```diff
   <!-- Required scripts -->
   <script type="text/javascript" src="js/qz-tray.js"></script>
   
   + <!-- jsrsasign library required for JavaScript signing. In production, download and reference a local copy!!-->
   + <script src="https://cdnjs.cloudflare.com/ajax/libs/jsrsasign/11.1.0/jsrsasign-all-min.js"></script>
   + <script src="assets/signing/sign-message.js"></script>
   ```

1. Find the `setSignaturePromise` function and remove it entirely from the `sample.html` page. We are using the function in our `sign-message.js` script.

That's it!