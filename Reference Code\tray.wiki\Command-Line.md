### Compatibility

* :white_check_mark: 2.2 | :warning: 2.1 | :no_entry: 2.0 | :no_entry: 1.9 [...](Roadmap)

### Contents

Advanced command line usage and environmental variable usage examples.  For compiling options, see [compiling](compiling).  For installer options, see [deployment](deployment).

### Environmental Variables


| Variable         | Description | Usage |
|------------------|-------------|-------|
| `_JAVA_OPTIONS`  | Sets [Java command line options](https://docs.oracle.com/en/java/javase/11/tools/java.html) for **all** Java VMs to be picked up.<br>:warning: **WARNING**: Use with caution, impacts all Java instances and applications. | `export _JAVA_OPTIONS=-Xmx4096m` |
| `QZ_OPTS`| Sets [Java command line options](https://docs.oracle.com/en/java/javase/11/tools/java.html) for **only <PERSON>Z Tray** to pick up.<br>(will **NOT** impact other Java applications)<br>Since 2.1.3 | `export QZ_OPTS=-Xmx4096m` <br>or<br> `export QZ_OPTS=-DtrustedRootCert=custom_cert.pem` |

> :bulb: Environmental variables will take effect after QZ Tray is restarted.

#### Windows Environment Variables
Windows environmental variables are often set through **Advanced System Settings, Advanced, Environment Variables**.  Alternatively,through the `set|setx` command.

```cmd
setx QZ_OPTS "-Xmx4096m"
```

... and to revert:

```cmd
setx QZ_OPTS ""
```

#### macOS Environment Variables

macOS environmental variables do not persist; set via `defaults write` instead:

```zsh
defaults write io.qz.qz-tray QZ_OPTS -string "-Xmx4096m"`
```

... and to revert:

```zsh
defaults delete io.qz.qz-tray QZ_OPTS
```

> :bulb: For custom branded (white-label) customers, this command will change to `defaults write <BUNDLE_ID>`.<br>... where `<BUNDLE_ID>` is the value returned from `java -jar /Applications/MyApp.app/my-app.jar --bundleid`

### Installer Options

See [deployment](deployment) steps.

### Command Line Usage
Since 2.2.0

```bash
# Windows
"C:\Program Files\QZ Tray\qz-tray-console.exe" [options]

# Mac
"/Applications/QZ Tray.app/Contents/MacOS/QZ Tray" [options]

# Linux
"/opt/qz-tray/qz-tray" [options]

```

```
Usage: java -jar qz-tray.jar (command)

INFORMATION
  --help, -h, /?                   Display help information and exit.
  --version, -v                    Display version information and exit.
  --bundleid, -i                   Display Apple bundle identifier and exit.
  --libinfo, -l                    Display detailed library version information and exit.

ACTION
  --allow, --whitelist, -a         Add the specified certificate to allowed.dat.
                                     java -jar qz-tray.jar --allow cert.pem
  --block, --blacklist, -b         Add the specified certificate to blocked.dat.
                                     java -jar qz-tray.jar --block cert.pem
  --file-allow                     Add the specified file.allow entry to qz-tray.properties for FileIO operations, sandboxed to a specified certificate if provided
                                     java -jar qz-tray.jar --file-allow /my/file/path [--sandbox "Company Name"]
  --file-remove                    Removes the specified file.allow entry from qz-tray.properties for FileIO operations
                                     java -jar qz-tray.jar --file-remove /my/file/path

OPTION
  --honorautostart, -A             Read and honor any autostart preferences before launching. [true]
  --steal, qz:steal                Ask other running instance to stop so that this instance can take precedence. [false]
  --headless                       Force startup "headless" without graphical interface or interactive components. [false]

INSTALLER
  preinstall                       Perform critical pre-installation steps: Stop instances, all other special considerations.
  install                          Copy to the specified destination and preforms platform-specific registration.
                                     java -jar qz-tray.jar install --dest /my/install/location [--silent]
  certgen                          Performs certificate generation and registration for proper HTTPS support.
                                     java -jar qz-tray.jar certgen [--key key.pem --cert cert.pem] [--pfx cert.pfx --pass 12345] [--host "list;of;hosts"]
  uninstall                        Perform all uninstall tasks: Stop instances, delete files, unregister settings.
  spawn                            Spawn an instance of the specified program as the logged-in user, avoiding starting as the root user if possible.
                                     java -jar qz-tray.jar spawn [program params ...]

BUILD
  jlink                            Download, compress and bundle a Java Runtime
                                     java -jar qz-tray.jar jlink [--platform mac|windows|linux] [--arch x64|aarch64] [--vendor bellsoft|eclipse|...] [--version ...] [--gc hotspot|openj9] [--gcversion ...]
  provision                        Provision/bundle addition settings or resources into this installer
                                     java -jar qz-tray.jar provision --json file.json [--target-os windows --target-arch x86_64]
For help on a specific command:
  Usage: java -jar qz-tray.jar --help (command)
    --help --file-allow
    --help install
    --help certgen
```

### System Properties

QZ Tray will honor Java system properties via:
* Command-line override: [`-Doption=value`](#command-line-usage)
* Environmental variables: [`QZ_OPTS`](#environmental-variables)
* Properties file: `qz-tray.properties` (**Advanced** --> **Diagnostic** --> **Browse App Folder**)
* Preferences file: `prefs.properties`* (**Advanced** --> **Diagnostic** --> **Browse User Folder**)<br>\*for properties which support user-scoped overrides

e.g.

```properties
# custom websocket port example
websocket.secure.ports=9191,9292
websocket.insecure.ports=9192,9293
```

| Property          | Description  | Example        |
|-------------------|--------------|----------------|
| `trustedRootCert` | Overrides the internal licensing certificate by reading the path provided. | `java -DtrustedRootCert=custom_cert.pem -jar qz-tray.jar ...` |
| `tray.notifications` | Show verbose connect/disconnect notifications in the tray area. | `java -Dtray.notifications=true -jar qz-tray.jar ...` |
| `tray.headless` | Force QZ Tray into headless mode | `java -Dtray.headless=true -jar qz-tray.jar ...` |
| `tray.monocle` | Enable/disable monocle JavaFX rendering engine | `java -Dtray.monocle=false -jar qz-tray.jar ...` |
| `tray.strictmode` | Use strict certificate mode | `java -Dtray.strictmode=true -jar qz-tray.jar ...` |
| `tray.idle.printers` | When idle, fetch printers in the background | `java -Dtray.idle.printers=false -jar qz-tray.jar ...` |
| `tray.idle.javafx` |  When idle, startup Java FX in the background | `java -Dtray.javafx=true -jar qz-tray.jar ...` |
| `security.file.enabled` | Enable/disable all File Communications features [true] | `java -Dsecurity.file.enabled=false -jar qz-tray.jar ...` |
| `security.file.strict` | Enable/disable signing requirements for File Communications features [true] | `java -Dsecurity.file.strict=false -jar qz-tray.jar ...` |
| `security.substitutions.enable` | Enable/disable client-side JSON data substitutions via "substitutions.json" file [true] (Since 2.2.4) | `java -Dsecurity.substitutions.enable=false -jar qz-tray.jar ...` |
| `security.substitutions.strict` | Enable/disable restrictions for materially changing JSON substitutions such as "copies":, "data": { "data": ... } blobs [true] (Since 2.2.4) | `java -Dsecurity.substitutions.strict=false -jar qz-tray.jar ...` |
| `security.data.protocols` | URL protocols allowed for print, serial, hid, etc [http,https] (Since 2.2.4) | `java -Dsecurity.data.protocols="ftp" -jar qz-tray.jar ...` |
| `security.print.tofile` | Enable/disable printing directly to file paths [false] (Since 2.2.4) |  `java -Dsecurity.print.tofile=true -jar qz-tray.jar ...` |
| `security.wss.snistrict` | Enables strict http/websocket SNI checks [false] (Since 2.2.4) |  `java -Dsecurity.wss.snistrict=true -jar qz-tray.jar ...` |
| `security.wss.httpsonly` | Disables insecure http/websocket ports (e.g. '8182') [false] (Since 2.2.4) | `java -Dsecurity.wss.httpsonly=true -jar qz-tray.jar ...` |
| `security.wss.host` | Influences which physical adapter to bind to by setting the host parameter for http/websocket listening [0.0.0.0] (Since 2.2.4) |  `java -Dsecurity.wss.host="***********" -jar qz-tray.jar ...` |
| `security.wss.alloworigin` | Override `Access-Control-Allow-Origin: *` HTTP response header for fine-grained control of incoming HTTP connections [*] (Since 2.2.5) | `java -Dsecurity.wss.alloworigin=https://demo.qz.io -jar qz-tray.jar ...`|
| `tray.file.enabled` | Enable/disable File IO | `java -Dtray.file.enabled=false -jar qz-tray.jar ...` |
| `tray.file.strict` | Enable/disable valid signatures for File IO | `java -Dtray.file.strict=false -jar qz-tray.jar ...` |
| `printer.status.jobdata` | Returns binary job data with printer job events (Since 2.2.2) | `java -Dprinter.status.jobdata=true -jar qz-tray.jar ...` |
| `websocket.secure.ports` | Comma separated list of secure websocket (wss://) ports to use [8181,8282,8383,8484] (Since 2.2.4) |  `java -Dwebsocket.secure.ports=9191,9292 -jar qz-tray.jar ...` |
| `websocket.insecure.ports` | Comma separated list of insecure websocket (ws://) ports to use [8182,8283,8384,8485] (Since 2.2.4) |  `java -Dwebsocket.insecure.ports=9192,9293 -jar qz-tray.jar ...` |
| `log.disable` | Disables logging. (Since 2.2.3) | `java -Dlog.disable=true -jar qz-tray.jar ...` |
| `log.size` | Modify the log file size before rotating to a new file (Since 2.2.3) | `java -Dlog.size=2097152 -jar qz-tray.jar ...` |
| `log.rotate` | Modifies the number of rotated log files. (Since 2.2.3) | `java -Dlog.rotate=9 -jar qz-tray.jar ...` |
