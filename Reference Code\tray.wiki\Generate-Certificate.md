### Compatibility
* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :white_check_mark: 2.0 | :white_check_mark: 1.9 | [...](Roadmap)

### Objective
* Generate a public certificate and a public/private key pair for signing messages.
* ⚠️ **Note:** For renewals, please see [renewal](renewal) steps instead.

### Steps

If you have purchased Premium Support, you can follow the steps below to generate a Certificate and Private Key pair to suppress dialogue warnings.  For renewals, please see [renewal](renewal) steps instead.

![image](https://cloud.githubusercontent.com/assets/12505463/22177749/b47a3de2-dff2-11e6-8fe6-4d27a262dff3.png)

1. Navigate to https://qz.io/login/
1. Enter the primary emaill address and product key and click **Sign In**
   > **Note:** Make sure there are no blank spaces before or after the product key.

1. Once signed in, select **QZ Tray**.

   <img width="361" alt="image" src="https://user-images.githubusercontent.com/6345473/213088981-37c3ca6f-38be-43ce-8bde-3e447d8a03b2.png">


1. At the next screen you have the ability to generate a CSR (Certificate Signing Request) and a public/private key pair.

   You may already have a CSR and/or a public/private key pair.  If that is the case, you can upload either of these at this screen.  It must be 2048-bit.
 If you do not upload anything, a public key, private key, and a certificate will be generated.

1. Fill in the required fields and hit **Submit request**
   > **NOTE:** Currently, you cannot generate the certificate/keys in Safari.  Other major browsers are all supported.
   
   <img width="1123" alt="image" src="https://user-images.githubusercontent.com/6345473/213089378-d4b12dba-5038-4ccc-aa5d-a34764038707.png">


1. After a few seconds a new field will appear at the bottom of the page.  Download the public key, private key, and certificate.

   * Download the appropriate private key format for your environment. Most environments require `PEM`, however `.NET` environments require PKCS#12 (`PFX`)

   * All three downloads will not be available if you provided QZ with a CSR or a public/private key pair.  The digital certificate will *always* be provided by QZ.  

   * This key chain will be used by QZ Tray to verify the authenticity of signed messages and to suppress the print warnings.

   <img width="1121" alt="image" src="https://user-images.githubusercontent.com/6345473/213089862-6008325d-db6a-4b8f-ac0c-cb434250fa73.png">

   * **Encrypt Private Key:** Optional.  Note, encrypted keys will not work with JavaScript signing examples.
   * **Private Key:** `private-key.pem` PKCS#8, `private-key.pfx` PKCS#12; needed for signing messages. This allows silent printing.
   * **Public Key:** `public-key.txt` x509; Not needed for printing, but is used to request new certificates.
   * **Digital Certificate (Trusted Certificate):** `digital-certificate.txt` Used on page load for silent printing.

   ### Important

   For security reasons, it is advised to store your private key in a secure location.  Your private key should *never* be given out to anybody.  If your private key is leaked, someone with malicious intents will be able to sign traffic on your behalf.

   If you believe your private key has been leaked, please let us know so we can blacklist this key.  

   > **Note:** The certificate and the private key are generated by *YOUR* web browser. This means we never get a copy of it, and could never pretend to be you while we print.

1. Now at that you have these keys, navigate to [signing](signing) tutorial for instructions on how to securely sign messages.