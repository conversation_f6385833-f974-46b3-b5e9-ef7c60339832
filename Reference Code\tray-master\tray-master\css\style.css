.btn-default {
    color: #333;
    background-color: #FFF;
    border-color: #CCC;
}

.btn-primary {
    color: #FFFFFF;
    background-color: #44AB53;
    border-color: #3D994A;
}

.btn-success {
    color: #FFF;
    background-color: #33658A;
    border-color: #2C5777;
}

.btn-warning {
    color: #FFF;
    background-color: #F6AE2D;
    border-color: #F5A515;
}

.btn-danger {
    color: #FFF;
    background-color: #F26419;
    border-color: #E5570D;
}

.btn-info {
    color: #FFF;
    background-color: #3680A8;
    border-color: #307195;
}

.btn-default:hover {
    background-color: #E6E6E6;
    border-color: #B3B3B3;
}

.btn-primary:hover {
    background-color: #358741;
    border-color: #2E7438;
}

.btn-success:hover {
    background-color: #254A65;
    border-color: #1E3C52;
}

.btn-warning:hover {
    background-color: #E6970A;
    border-color: #CE8709;
}

.btn-danger:hover {
    background-color: #CC4E0C;
    border-color: #B4450A;
}

.btn-info:hover {
    background-color: #2A6382;
    border-color: #24546E;
}

.btn-default:focus {
    background-color: #F2F2F2;
    border-color: #BFBFBF;
}

.btn-primary:focus {
    background-color: #3D994A;
    border-color: #358741;
}

.btn-success:focus {
    background-color: #2C5777;
    border-color: #254A65;
}

.btn-warning:focus {
    background-color: #F5A515;
    border-color: #E6970A;
}

.btn-danger:focus {
    background-color: #E5570D;
    border-color: #CC4E0C;
}

.btn-info:focus {
    background-color: #307195;
    border-color: #2A6382;
}

.btn-default:active {
    background-color: #E6E6E6;
    border-color: #B3B3B3;
}

.btn-primary:active {
    background-color: #358741;
    border-color: #2E7438;
}

.btn-success:active {
    background-color: #254A65;
    border-color: #1E3C52;
}

.btn-warning:active {
    background-color: #E6970A;
    border-color: #CE8709;
}

.btn-danger:active {
    background-color: #CC4E0C;
    border-color: #B4450A;
}

.btn-info:active {
    background-color: #2A6382;
    border-color: #24546E;
}

.btn-default.disabled {
    background-color: #FFFFFF;
    border-color: #CFCFCF;
}

.btn-primary.disabled {
    background-color: #45AF55;
    border-color: #3E9C4C;
}

.btn-success.disabled {
    background-color: #34688E;
    border-color: #2D5A7B;
}

.btn-warning.disabled {
    background-color: #F6B032;
    border-color: #F5A619;
}

.btn-danger.disabled {
    background-color: #F2671E;
    border-color: #E9590D;
}

.btn-info.disabled {
    background-color: #3783AC;
    border-color: #317499;
}

a {
    color: #3680A8;
}

a:hover,
a:focus {
    color: #24546E;
}

.text-primary {
    color: #44AB53;
}

.text-danger {
    color: #F26419;
}

.form-control .has-error {
    border-color: #EED3D7;
}

.form-control .has-success {
    border-color: #D6E9C6;
}

.navbar-default {
    background-color: #44AB53;
}

.navbar-default .navbar-nav > li > a,
.navbar-default .navbar-nav > li > a:hover,
.navbar-default .navbar-header .navbar-brand {
    color: #FFF;
}

@media (max-width: 768px) {
    .navbar-default .navbar-nav .open .dropdown-menu > li > a {
        color: #FFF;
    }
}

.navbar-default .navbar-nav > li > a:hover {
    background-color: #3D994A;
}

.navbar-default .navbar-toggle > .icon-bar {
    background-color: #FFF;
}

.navbar-default .navbar-toggle:focus > .icon-bar {
    background-color: #555;
    color: #24546E;
}

.navbar-default .navbar-toggle:hover > .icon-bar {
    background-color: #FFF;
}

.navbar-default .navbar-toggle:hover {
    background-color: #3D994A;
}

.panel.panel-primary {
    border-color: #3D994A;
}

.panel.panel-primary .panel-heading {
    border-color: #3D994A;
    background-color: #3D994A;
}

.alert.alert-warning a {
    color: #68522C;
}

.alert.alert-danger a {
    color: #B4450A;
}

html {
    position: relative;
    min-height: 100%;
}

body {
    padding-bottom: 80px;
}

table {
    font-size: inherit;
}

.versiontable > tbody > tr > th {
    text-align: right;
}

.versiontable > thead > tr > th {
    vertical-align: top;
}

.homesection + .homesection {
    border-top: solid 1px #EEE;
}

.homesection {
    text-align: center;
    margin: 40px 0;
    padding: 40px 0;
}

img.jumbo {
    margin-top: -40px;
    margin-left: -60px;
}

h1.jumbo {
    margin-top: 0;
}

footer {
    text-align: center;
    height: 80px;
    position: absolute;
    bottom: 0;
    width: 100%;
}

@media (min-width: 768px) {
    h1.jumbo,
    h3.jumbo {
        text-align: right;
    }
}

@media (max-width: 767px) {
    h1.jumbo,
    h3.jumbo {
        text-align: center;
    }
}

@media (min-width: 1200px) {
    h1.jumbo {
        margin-top: 40px;
    }
}

@media (min-width: 768px) {
    h3.jumbo {
        margin-top: -6px;
    }
}

.vertical-align {
    display: flex;
    align-items: center;
}

.hide_ {
    display: none;
}

.dropdown-submenu {
    position: relative;
}

.dropdown-submenu > .dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -6px;
    margin-left: 10px;
    -webkit-border-radius: 0 6px 6px 6px;
    -moz-border-radius: 0 6px 6px 6px;
    border-radius: 0 6px 6px 6px;
}

.drag-target {
    /* color: white; */
  background: repeating-linear-gradient(
    45deg,
    #dbf0de,
    #dbf0de 10px,
    #a5d9ad 10px,
    #a5d9ad 20px
  );
}

input[type="file"] {
    display: none;
}

button > .fa-caret-down {
    height: 100%;
    display: inline;
    margin-left: -.75em;
    margin-right: -1em;
}

@media (min-width: 768px) {
    .dropdown-submenu:hover > .dropdown-menu {
        display: block;
    }

    .dropdown-submenu > .dropdown-menu {
        margin-left: -1px;
    }
}

.dropdown-submenu > a:after {
    display: block;
    content: " ";
    float: right;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 5px 0 5px 5px;
    border-left-color: #CCCCCC;
    margin-top: 5px;
    margin-right: -10px;
}

.dropdown-submenu:hover > a:after {
    border-left-color: #FFFFFF;
}

.dropdown-submenu.pull-left {
    float: none;
}

.dropdown-submenu.pull-left > .dropdown-menu {
    left: -100%;
    margin-left: 10px;
    -webkit-border-radius: 6px 0 6px 6px;
    -moz-border-radius: 6px 0 6px 6px;
    border-radius: 6px 0 6px 6px;
}

.tip {
    border-bottom: 1px dashed;
}

summary {
    cursor: pointer;
}

code {
    color: #31708F;
    background-color: #D9EDF7;
}

input[type='radio'] {
    margin-left: .5em;
}

#printersLog {
    max-height: 20em;
    overflow: auto;
}

#printersLog .INFO {
    color: #303030;
}

#printersLog .WARN {
    color: #9F7435;
}

#printersLog .FATAL {
    color: #9F3535;
}
