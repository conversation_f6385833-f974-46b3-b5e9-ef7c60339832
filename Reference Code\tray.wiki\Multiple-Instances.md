### Compatibility
* :white_check_mark: 2.2 | :white_check_mark: 2.1* | :no_entry: 2.0 | :no_entry: 1.9 | [...](Roadmap)

### Objective

Explain the caveats and workarounds to running QZ Tray on a multi-user workstation.

### Singleton

QZ Tray is a [singleton](https://en.wikipedia.org/wiki/Singleton_pattern) application and it **cannot** support multiple simultaneous instances on the same machine because QZ Tray uses a localhost websocket connection (`wss://`) from the browser to a single port on the PC.  If a second instance of QZ Tray were allowed, the websocket would only ever succeed to the first available port, rendering the second instance useless.

Some options to help with this limitation:

* If your environment requires **multiple users using QZ Tray simultaneously**, consider setting up a dedicated [Print Server](print-server) for this.
* If your environment is happy having a [service account](windows-service) run the software, try to start QZ Tray as a persistent user on that machine.  Note: The printers available will those available or assigned to the service account. 
* If it's suitable for ANY user to be running QZ Tray, consider [globally-allowing](https://qz.io/docs/headless#steps) the certificate.
* If your environment only requires **one user using QZ Tray at a time**, see [Stealing Instance](#stealing-instance) below.

### Symptoms


Since QZ Tray will start automatically with the computer, this singleton limitation can pose a problem on a shared workstation.  By default, QZ Tray will attempt to start automatically upon login however if another user is running QZ Tray, it will detect the other instance and quietly shutdown. This is usually observed by the following behavior:

> "QZ Tray icon disappears from the system tray"

> "When I double-click on the QZ Tray icon, it shows up for a second and then goes away"

> "There's link on the web page to start QZ Tray (e.g. `qz:launch` but it doesn't work if someone else is signed onto the computer"

### Stealing Instance

Since 2.1.4, QZ Tray supports [stealing another instance](https://github.com/qzind/tray/issues/596), which can be helpful on shared workstations.  There are several ways to toggle this behavior on:
* Administrators can set an [environment variable](https://user-images.githubusercontent.com/6345473/128921894-4e25e825-d4b0-4f77-9e03-472cde20891f.png) called `QZ_OPTS` to `-DstealWebsocket=true`.<br>-- OR --
* Administrators can edit `qz-tray.properties` and add a line `websocket.steal=true`.  This file can be found by clicking QZ Tray, Advanced, Diagnostic, Browse App folder (secure environments may store this file in the User or Shared folder)
   ```properties
   #Sat Jan 1 00:00:00 BOT 2022
   websocket.steal=true # <--------- Steal Websocket
   wss.host=0.0.0.0
   wss.alias=qz-tray
   ca.alias=root-ca
   ca.storepass=a1b2c3d4
   wss.storepass=a1b2c3d4
   ca.keystore=root-ca.p12
   wss.keystore=qz-tray.p12
   networking.hostname=yahoo.com
   ```
   -- OR --
* Administrators can start QZ Tray with the [command-line](command-line) flag `--steal`<br>-- OR --
* Web developers can start QZ Tray with the hyperlink `qz:steal` (instead of `qz:launch`)
   * **Note:** `qz:steal` is NOT currently supported on Mac, please use the properties or environmental technique instead.
* In a multi-user environment, client environments can choose to detect the user login event and steal the QZ instance. Examples provided here:
   * Batch: [`start_if_active.bat`](https://gist.github.com/tresf/ff91bd29f1f6846c13bf6f8bc255ab55)
   * PowerShell: [`start_if_active.bat.ps1`](https://gist.github.com/tresf/4e19e15ad38354af6732ee701c990102)


