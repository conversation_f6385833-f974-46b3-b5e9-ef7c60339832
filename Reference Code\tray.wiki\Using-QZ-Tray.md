### Compatibility

* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :white_check_mark: 2.0 | :white_check_mark: 1.9 | [...](Roadmap)

### Objective

This guide instructs how to properly download and install QZ Tray so that HTTPS is supported in all browsers on all operating systems.  Currently Firefox is the only browser that requires any [additional steps](#firefox-steps).

The screenshots may look different depending on your operating system and the version of QZ Tray you are running, but the steps in this article should be nearly identical.

### Installing QZ Tray

1. [Install Java 7 or higher](https://java.com/en/download) from Oracle's website.

   > **Note:** QZ Tray 2.1 and HTML printing require Java 8 or higher.  See detailed [system requirements](faq#minimum-system-requirements).

1. Download the appropriate version of QZ Tray for your operating system

   <a href="https://qz.io/download"><img width="294" alt="image" src="https://user-images.githubusercontent.com/6345473/213087687-3f82debd-f42e-4728-8410-cdbbc46fb0ea.png"></a>

1. Install, taking the defaults.

   <img width="447" alt="Screenshot 2023-01-17 at 11 58 45 PM" src="https://user-images.githubusercontent.com/6345473/213088123-a04af54a-0cce-4ee9-a4f5-9a2433acb706.png">


### Firefox steps

   The following steps are required for Windows, Linux and Mac to gain HTTPS support in Firefox.

 * Currently, in order for HTTPS to work with Firefox, QZ Tray must be installed (or reinstalled) **AFTER** Firefox.
 * The certificate installation is performed when Firefox is opened, so closing all running versions of Firefox is required for HTTPS support.
  * **Mac** users will need to fully quit Firefox via Command (⌘) + Q.

### Testing QZ Tray

1. Open install folder via **Tray Icon > Advanced > Open File Location**

1. Open `sample.html` from the demo folder.  By default, this is located in `C:\Program Files\QZ Tray\demo` on Windows.
   * To test accessing this from a webserver, navigate to https://demo.qz.io

1. When prompted to allow access, click **Allow**

   ![image](https://cloud.githubusercontent.com/assets/12505463/22177889/d9f842aa-dff5-11e6-8f97-af662e87b323.png)

1. If the page is loaded properly, you will now be able to use the buttons provided in the `sample.html` file.

   * To verify that the connection was established over HTTPS, **right click > Inspect element** on the webpage and look in the console (at the top).  

   * Ports 8181, 8282, 8383, and 8484 are HTTPS connections.  Ports 8182, 8283, 8384, and 8485 are HTTP connections.

   ![image](https://cloud.githubusercontent.com/assets/12505463/12439975/ce510dfa-bf04-11e5-8f0f-96b08ff65787.png)


### What next?

Now that QZ Tray is properly running, you can start integrating the code into your environment!

1. Learn how to sign your messages to suppress all dialogue warnings:
   * [Signing messages in QZ Tray](signing)

1. Learn the minimal code needed to print from your browser to your printer:
   * [Bare-bone example in QZ Tray](getting-started)

1. Learn how to run QZ Tray as a dedicated service (print server):
   * [Create a print server in QZ Tray](print-server)

 