Create a QZ Tray distribution

This assumes [Windows signing](https://github.com/qzind/tray/wiki/Compiling#ant-properties-file) and [macOS package signing](https://github.com/qzind/tray/blob/2.0/ant/apple/apple.properties#L10) have been configured.

# Update the version
* `src/qz/common/Constants.java`
* `js/qz-tray.js` (two places)
* `js/package.json` (for npm)

Before [publishing](#publish) make sure to commit the above version changes.  We usually just call this [`Bump version`](https://github.com/qzind/tray/search?q=bump+version&type=Commits).

# Create the builds
Chain all three builds on macOS, following [compiling](https://github.com/qzind/tray/wiki/Compiling) tutorial.

```bash
ant pkgbuild && cp out/*.pkg ~/Desktop/ && \
ant makeself && cp out/*.run ~/Desktop/ && \
ant -Dtarget.arch=x86_64 nsis && cp out/*.exe ~/Desktop/ && \
ant -Dtarget.arch=x86_64 pkgbuild && cp out/*.pkg ~/Desktop/ && \
ant -Dtarget.arch=x86_64 makeself && cp out/*.run ~/Desktop/ && \
ant -Djlink.java.version=21.0.7+9 -Dtarget.arch=riscv64 makeself && cp out/*.run ~/Desktop && \ 
echo "Done"
```

# Sign Windows Release

As of QZ Tray 2.2.5, this is done automatically by the build system using Amazon KMS

<details>
<summary>Click to expand old steps</summary>

Sign the `.exe` using DigiCert EV cert on Windows.  Note, if you're on a High DPI screen, the app might look weird.  Use [this regkey and manifest to fix it](https://github.com/qzind/tray/files/2517135/PreferExternalManifest.zip).
* Insert the USB hardware key (if it's not ready, read [this](https://www.digicert.com/code-signing/ev-code-signing-dual-signing-sha256-sha1.htm) first)
* Download the [`DigiCertUtil.exe`](https://www.digicert.com/util/)
* Wait for Windows to setup the hardware key (about 5 minutes)
* If it's the first time using the key on this computer, click "Repair cert"
* Sign using SHA1, then SHA2
   * When prompted for a password, Windows 10 may ask for a `PIN`, but actually use the full hardware password.
</details>

# Notarize macOS Release
* Send the package to Apple for [notarization](https://github.com/qzind/tray/issues/372):

   ```bash
      xcrun notarytool submit --wait qz-tray-x.x.x-arm64.pkg --wait --apple-id <developer-id>@qz.io --password <the-secure-password> --team-id <team-id>
   ```

   * `apple-id`: The email of the apple developer account 
   * `password`: The application-specific password (NOT the Apple developer password!)
   * `team-id`: The signing ID, e.g. `P5DMU6659X`

* After a few minutes, it returns:

   ```diff
   Processing complete
      id: abcdefgh-abcd-1234-abcd-abcdefghij
   +  status: Accepted
   ```

* Which then can be monitored remotely using:

   ```bash
   xcrun notarytool log abcdefgh-abcd-1234-abcd-abcdefghij --apple-id <developer-id>@qz.io --password <the-secure-password> --team-id <team-id>
   ```

* Finally, staple the installer:
   ```bash
   xcrun stapler staple qz-tray-x.x.x-arm64.pkg
   ```

   ```diff
   Processing: qz-tray-x.x.x-arm64.pkg
   Processing: qz-tray-x.x.x-arm64.pkg
   + The staple and validate action worked!
   ```

# Publish
* Upload builds to https://github.com/qzind/tray/releases/new
   * Tag: `v2.x.x` (remember the `v`)
   * Name: `2.x.x`
* Create simple, effective release notes.  e.g.

   ```markdown
   **201X-01-01**

   **Features**

   * CP-987 support (#954)

   **Fixes**

   * Fixes HTTPS connections (#956)
   * Better signing example for js (a1b2c3d)
   ```

* Email premium clients (`qz.io` (slash) `admin`)
   * Include abridged version of release notes
   * Link to download
   * Provide any additional information (such as status of 2.x.x beta)

* Post to [Facebook](https://facebook.com/qzind), [Twitter](https://twitter.com/qzind)

* Update `npm`
Assumes `npm` is installed (e.g. `brew install npm`)
   * Open a terminal to `js` (e.g. `~/tray/js`)
   * Ensure `package.json` version is up to date
   * If not already, call `npm adduser`
       * User: `qzind`
       * Public Email: `<EMAIL>`
   * Finally, call `npm publish`
