### Compatibility

* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :white_check_mark: 2.0 | :no_entry: 1.9 | [...](Roadmap)

Video tutorials:

* [Getting Started Video](https://www.youtube.com/watch?v=wKIY4gqkIFE)

### Background

 * What is QZ Tray?  It's a cross-browser, cross-platform plugin for printing (and talking to serial/usb devices).
 * QZ Tray ships with a `sample.html` page to demonstrate its features.
 * The `sample.html` is bundled with the desktop software located in:
    * Windows: `C:\Program Files\QZ Tray\demo`
    * MacOS: `/Applications/QZ Tray.app/Contents/Resources/demo`
       * 2.1 and older is located at `/Applications/QZ Tray.app/demo`
    * Linux: `/opt/qz-tray/demo`
 * Need to test HTTPS support?  Visit https://demo.qz.io.
 * For raw printing, some systems may require the [Linux](setting-up-a-raw-printer-in-ubuntu-linux), [Windows](setting-up-a-raw-printer-in-windows) or [Mac](setting-up-a-raw-printer-in-osx) raw printer setup guide.

## Objective

Illustrate how to print from a web browser or web app with QZ Tray.

## The Code

1. In the `demo` folder of QZ Tray (`QZ Tray/demo/js`) there are several JavaScript files.  Only one is essential for QZ Tray.


   | File                                | Description                 | Required |
   |-------------------------------------|-----------------------------|----------|
   | `js/qz-tray.js`                     | QZ Tray websocket wrapper | :white_check_mark: Yes |
   
   > **Note:** Optionally, you may [`npm install qz-tray`](https://www.npmjs.com/package/qz-tray)
   ```html
     <script type="text/javascript" src="js/qz-tray.js"></script>
   ```

<details>
<summary>(deprecated) Click to expand dependencies for 2.0 version</summary>

| File                                | Description                 | Required |
   |-------------------------------------|-----------------------------|----------|
   | `js/dependencies/rsvp-3.1.0.min.js` | ECMAScript 6 Promise lib | :white_check_mark: Yes, unless [overriden](api-overrides). |
   | `js/dependencies/sha-256.min.js` | SHA-256 hashing lib | :white_check_mark: Yes, unless [overriden](api-overrides#override-sha256). |
   | `js/qz-tray.js`                     | QZ Tray websocket wrapper | :white_check_mark: Yes |
   
   > **Note:** Optionally, you may [`npm install qz-tray sha ws`](https://www.npmjs.com/package/qz-tray) and optionally `q`|`bluebird`|`rsvp`.
   ```js
     <head>
     <meta charset="UTF-8">
     <script type="text/javascript" src="js/dependencies/rsvp-3.1.0.min.js"></script>
     <script type="text/javascript" src="js/dependencies/sha-256.min.js"></script>
     <script type="text/javascript" src="js/qz-tray.js"></script>
     </head>
   ```

</details>

1. This next portion of the code deploys QZ Tray by calling [`qz.websocket.connect()`](https://qz.io/api/qz.websocket#.connect) to bind to a local websocket instance of the running software.

   #### Connect

   ```js
   qz.websocket.connect().then(function() {
      alert("Connected!");
   });
   ```
   :bulb: **Note:** To keep trying the connection after failure:
     * [`qz.websocket.connect({ retries: 5, delay: 1}).then(...);`](https://qz.io/api/qz.websocket#.connect).
     * Need to launch QZ from the click of a button?  You can [do that too](https://gist.github.com/tresf/42a8f25124840a6c496b).
1. This next code snippet calls [`qz.printer.find(..)`](https://qz.io/api/qz.printers#.find) to find a printer name containing the word "zebra" (e.g. `Zebra LP2844`, etc) . This can only be called after a successful connection.

   #### Find Printer

   ```js
   qz.printers.find("zebra").then(function(found) {
      alert("Printer: " + found);
   });
   ```

   * You can also list all of the printers attached to the system:

   ```js
    function findPrinters() {
      qz.printers.find().then(function(data) {
         var list = '';
         for(var i = 0; i < data.length; i++) {
            list += "&nbsp; " + data[i] + "<br/>";
        }
        displayMessage("<strong>Available printers:</strong><br/>" + list);
     }).catch(function(e) { console.error(e); });
   }
   ```

   ⚠️ **Warning:** Mac users: Due to discrepancies in printer search behavior, the printer may be listed differently to Java versus the System Preferences.  e.g. `Zebra LP2844` may list in Java as `Zebra_LP2844`.  See [`qz-print/131`](https://github.com/qzind/qz-print/issues/131#issuecomment-170973569) for more information.

1. Finally, we send the printer some data using [`qz.print(...)`](https://qz.io/api/qz#.print).  This can only be called after a successful connection.

   #### Send Data

   ```js
   var config = qz.configs.create("Zebra LP2844-Z");               // Exact printer name from OS
   var data = ['^XA^FO50,50^ADN,36,20^FDRAW ZPL EXAMPLE^FS^XZ'];   // Raw commands (ZPL provided)

   qz.print(config, data).then(function() {
      alert("Sent data to printer");
   });
   ```

1. That's it! Now chain them all together.

   #### Chaining Requests

   ```js
   qz.websocket.connect().then(function() { 
      return qz.printers.find("zebra");              // Pass the printer name into the next Promise
   }).then(function(printer) {
      var config = qz.configs.create(printer);       // Create a default config for the found printer
      var data = ['^XA^FO50,50^ADN,36,20^FDRAW ZPL EXAMPLE^FS^XZ'];   // Raw ZPL
      return qz.print(config, data);
   }).catch(function(e) { console.error(e); });
   ```

<details>
<summary>(deprecated) Click to expand 1.9 to 2.0 migration guide</summary>

## Migration Guide

##### Function matrix

| 1.9                         | 2.0                            |
| ----------------------------|--------------------------------|
| Callback driven, synchronous | [Promise](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise) driven, asynchronous |
| `deployQZ()`                | [`qz.websocket.connect()`](#connect)       |
| `qz.findPrinter(...)`       | [`qz.printers.find(..)`](#find-printer)              |
| `qz.append(...)`            | No direct equivalent.  See [Raw Printing](raw#generic) example.               |
| `qz.appendImage(...)`       | No direct equivalent.  See [Image Printing](pixel#image-printing) example.               |
| `qz.appendHTML(...)`        | No direct equivalent.  See [HTML Printing](pixel#html-printing) example.               |
| `qz.print()`                | [`qz.print(config, data)`](raw#generic)                   |
| `qz.printPS()`              | [`qz.print(config, data)`](pixel)                   |
| `qz.printHTML()`            | [`qz.print(config, data)`](pixel#html-printing)                   |
| `qz.setPaperSize(...)`      | [`qz.configs.create(..., { units: 'in', size: { width: 8.5, height: 11 } });`](pixel#page-size)|
| `qz.setAutoSize(...)`       | [`qz.configs.create(..., { scaleContent: ... });`](pixel#disable-autoscale) |
| `qz.setOrientation(...)`    | [`qz.configs.create(..., { orientation: ... });`](pixel) |
| `qz.setEncoding(...)`       | `qz.configs.create("...", { encoding: 'UTF-8' /* etc */ });` |
| `qz.findPorts()`            | [`qz.serial.findPorts()`](serial#list-serial-ports)
| `qz.openPort(...)`          | [`qz.serial.openPort(...)`](serial#open-and-close-ports)
| `qz.closePort(...)`         | [`qz.serial.closePort(...)`](serial#open-and-close-ports)
| `qzReady()`                 | `qz.websocket.connect()`[**`.then(function() { ... })`**](#chaining-requests) |
| `qzDoneFinding()`           | `qz.printers.find(...)`[**`.then(function() { ... })`**](#chaining-requests) |
| `qzDoneAppending()`         | No direct equivalent, resolves promise [at print time](pixel#image-printing). |
| `qzDoneFindingPorts()`      | `qz.serial.findPorts()`[**`.then(function() { ... })`**](serial#list-serial-ports) | 
| `qzDoneOpeningPort()`       | `qz.serial.openPort(..., ...)`[**`.then(function() { ... })`**](serial#open-and-close-ports) |

</details>

## What Next?

 * Looking for another language?  See our [Raw Printing](raw) tutorial for [ZPL](raw#zpl), [EPL](raw#epl), [SBPL](raw#sbpl), [ESC/P](raw#escp), [FGL](raw#fgl) or try our [Generic](raw#generic) method.
 * QZ Tray can do much more than print [raw commands](raw). It can print:
  * [PDFs](pixel#pdf-printing), [HTML](pixel#html-printing), [Images](pixel#image-printing)
 * It can send and receive data from:
  * [USB](usb) and [Serial](serial) attached devices
 * It can print silently, but only if messages are [digitally signed](signing).