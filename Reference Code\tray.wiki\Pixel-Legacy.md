### Compatibility

* :white_check_mark: 2.0 | :no_entry: 1.9 | [...](Roadmap)

---

### Deprecation Notice

:warning: This API is deprecated.  Please use the 2.1 API [here](pixel) with the latest [`qz-tray.js`](https://github.com/qzind/tray/blob/master/js/qz-tray.js), which is fully backwards compatible with the 2.0 version.

---

### Background

These features may not work with many [raw printing devices](what-is-raw-printing), such as Zebra, Epson, Citizen printers. The data is sent in Pixel Printing format, which is more common for LaserJet or Deskjet printers.  The `ZDesigner` driver from Zebra allows both raw and pixel printing.

To test these features without a physical printer, use a [printer emulator](printer-emulators#pixel)

### Contents

The following code can be used for Pixel Printing (formerly Postscript Printing) only. If you are unsure what Pixel Printing is, please refer to [What is Pixel Printing?](2.0-pixel-printing)

* [HTML Printing](#html-printing)
   * [Legacy HTML](#legacy-html)
* [PDF Printing](#pdf-printing)
* [Image Printing](#image-printing)
* [Advanced Pixel Printing](#advanced-printing)
   * [Specifying Custom Page Size](#page-size) | [Dymo LabelWriter](#dymo-labelwriter)
   * [Overriding Default Margins](#margins)
   * [Disabling Auto-Scaling of Content](#disable-autoscale)
   * [Forcing Image Interpolation](#image-interpolation)
   * [Specifying Copies](#copies)
   * [Specifying Custom Job Name](#custom-job-name)
   * [Vector PDF Printing (Disable Image Rasterization)](#rasterize)
* [Chaining Print Requests](#chaining-requests)
* [Queuing Print Requests](#promise-loop)

***

### HTML Printing

1. HTML rendering is done via pure Java in using an embedded webkit browser

   ```js
   var config = qz.configs.create("Printer Name");
   var data = [{
      type: 'html',
      format: 'file', // or 'plain' if the data is raw HTML
      data: 'assets/html_sample.html'
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

1. An [`options` parameter](https://qz.io/api/qz.configs#.setDefaults) can be supplied to signify things including margins, orientation, size, printer language, encoding, etc.

   #### Orientation

   ```js
   var config = qz.configs.create("Printer Name", { margins: 2, orientation: 'landscape'});
   var data = [{
      type: 'html',
      format: 'file', // or 'plain' if the data is raw HTML
      data: 'assets/html_sample.html'
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

   #### Page Width
   A [`print` parameter](https://qz.io/api/qz#.print) can be supplied to force a particular HTML page width.  If you're experiencing layout problems, please see [this explanation](https://github.com/qzind/tray/issues/193#issuecomment-301122429).

   ```js
   var config = qz.configs.create("Printer Name");
   var data = [{
      type: 'html',
      format: 'file', // or 'plain' if the data is raw HTML
      data: 'assets/html_sample.html',
      options: { pageWidth: 8 }
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

   #### Page Height
   A [`print` parameter](https://qz.io/api/qz#.print) can be supplied to force a particular HTML page height.  If you're experiencing layout problems, please see [this explanation](https://github.com/qzind/tray/issues/193#issuecomment-301122429).

   ```js
   var config = qz.configs.create("Printer Name");
   var data = [{
      type: 'html',
      format: 'file', // or 'plain' if the data is raw HTML
      data: 'assets/html_sample.html',
      options: { pageHeight: 11.5 }
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

   #### JavaFX
   * :warning: Java 8 + [JavaFX](https://gluonhq.com/products/javafx/) is required for high-resolution (>72 dpi) HTML printing.
     * :warning: Linux users: By default, JavaFX is **NOT** included in OpenJDK
   * :bulb: We recommend using AdoptOpenJDK 11 for all Operating Systems. We have written an installation tutorial: [Upgrading Java](upgrading-java)

   #### Legacy HTML
   Since 2.0.6, a [`legacy`](https://qz.io/api/qz.configs#.setDefaults) parameter can be supplied to force QZ Tray to emulate HTML printing engine from QZ Tray 1.9 and older.  For receipt printers, it's recommend to add `style="margin-left: 8px;"` to mimic the margins from 1.9.

   ```js
   var config = qz.configs.create("Printer Name", { legacy: true });
   var data = [{
      type: 'html',
      format: 'file', // or 'plain' if the data is raw HTML
      data: 'assets/html_sample.html',
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

### PDF Printing

QZ Tray 2.0 allows printing PDF files directly to a printer using Apache PDFBOX.

1. How to print a PDF file

   ```js
   var config = qz.configs.create("Printer Name");
   var data = [{ 
         type: 'pdf', 
         data: 'assets/pdf_sample.pdf' 
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

   #### Base64 PDF

1. How to print a base64 PDF
   ```js
   var config = qz.configs.create("Printer Name");
   var data = [{ 
      type: 'pdf',
      format: 'base64',
      data: 'Ck4KcTYwOQpRMjAzLDI2CkI1LDI2LDAsMUEsMyw3LDE1MixCLCIxMjM0IgpBMzEwLDI2LDAsMywx...' 
   }];
   qz.print(config, data).catch(function(e) { console.error(e); });
   ```

### Image Printing
```js
var config = qz.configs.create("Printer Name");
var data = [{ 
   type: 'image', 
   data: 'assets/img/image_sample.png' 
}];
qz.print(config, data).catch(function(e) { console.error(e); });
```

#### Base64 Image
```javascript
var config = qz.configs.create("Printer Name");
var data = [{ 
   type: 'image', 
   format: 'base64',
   data: 'AAAA...==' 
}];
qz.print(config, data).catch(function(e) { console.error(e); });
```

### Advanced Printing
#### Page Size
A page size can be set using the config parameter [`size`](https://qz.io/api/qz.configs#.setDefaults). 

 * Both standard and metric sizes are supported.  Warning, providing a metric page size will assume the [`density`](https://qz.io/api/qz.configs#.setDefaults) is in a metric format as well.

##### Dymo LabelWriter
The following example illustrates how to print a `2.25in x 1.25in` label to a Dymo LabelWriter printer.
 * [`size`](https://qz.io/api/qz.configs#.setDefaults) is provided in inches.  Alternatively `57.15mm x 31.75mm` can be provided if metric units are desired.
 * [`interpolation`](https://qz.io/api/qz.configs#.setDefaults) is provided to prevent barcode blurring.
 * [`colorType`](https://qz.io/api/qz.configs#.setDefaults) is provided to maximize driver quality compatibility/300dpi dithering.

```js
   function printStuff() {
      var config = qz.configs.create("Printer Name", {
         size: {width: 2.25, height: 1.25}, units: 'in', 
         colorType: 'grayscale', 
         interpolation: "nearest-neighbor" 
      });

      var data = [{ 
         type: 'image', 
         data: 'assets/img/image_sample.png' 
      }];

      qz.print(config, data).catch(function(e) { console.error(e); });
   }
```

#### Margins

 A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `margins` can be provided to change the default page margins around the content.

```js
 var config = qz.configs.create("Printer Name", { margins: { top: 0.25, right: 0.25, bottom: 0.25, left: 0.25 } 
 });
 var data = [{ 
   type: 'image', 
   data: 'assets/img/image_sample.png'
 }];
 qz.print(config, data).catch(function(e) { console.error(e); });
```

#### Image Interpolation

A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `interpolation` can be provided to change the pixel blending technique used when scaling an image.

```js
var config = qz.configs.create("Printer Name", { interpolation: "nearest-neighbor" });
var data = [{ 
   type: 'image', 
   data: 'assets/img/image_sample.png'
}];
qz.print(config, data).catch(function(e) { console.error(e); });
```

#### Density
Not providing a density will cause printing to use the default value for the printer. A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `density` can be provided to change the DPI, dots/mm or dots/cm respectively.  Since 2.0.2, multiple `density` values can be provided in an array and the first compatible density will be chosen.

```js
var config = qz.configs.create("Printer Name", { units: "in", density: "600" });  // force 600dpi
var data = [{ 
   type: 'pdf', 
   data: 'assets/pdf_sample.pdf' 
}];
qz.print(config, data).catch(function(e) { console.error(e); });
```

#### Copies
A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `copies` can be provided to send the specified raw commands multiple times.  This is a convenience function as most raw languages already have an internal copies command.
   ```js
function copies() {
   var config = qz.configs.create("Printer Name", { copies: 4 });

   var data = [{ 
      type: 'pdf', 
      data: 'assets/pdf_sample.pdf' 
   }];

   qz.print(config, data).catch(function(e) { console.error(e); });
}
   ```


#### Custom Job Name
A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `jobName` can be provided to change the name listed in the print queue.

```js
var config = qz.configs.create("Printer Name", { jobName: "Receipt #123456" });
var data = [{ 
   type: 'image', 
   data: 'assets/img/image_sample.png' 
}];
qz.print(config, data).catch(function(e) { console.error(e); });
```

#### Disable Autoscale
By default, content is automatically scaled to the destination page size.  This is done as a courtesy to take out the guesswork in fitting the content to the media.  A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `scaleContent` can be forced to prevent auto-scaling.

```js
var config = qz.configs.create("Printer Name", { scaleContent: "false" });  // do not stretch image to page width
var data = [{ 
   type: 'image', 
   data: 'assets/image_sample.png' 
}];
qz.print(config, data).catch(function(e) { console.error(e); });
```

#### Rasterize
In some cases, rather than rasterizing print output, the vector version is preferred.  This can have both quality as well as performance benefits during the printing process.  A [`config`](https://qz.io/api/qz.configs#.setDefaults) parameter `rasterize` can be forced to prevent software rasterization prior to printing.

   * :warning: This feature is currently only effective with PDF printing, which can cause [hard-crashes on OS X with older java versions](https://issues.apache.org/jira/browse/PDFBOX-2682).

```js
var config = qz.configs.create("Printer Name", { rasterize: "false" });  // use vector rendering
var data = [{ 
   type: 'pdf', 
   data: 'assets/pdf_sample.pdf' 
}];
qz.print(config, data).catch(function(e) { console.error(e); });
```

### Chaining Requests
Print requests can be chained together to print several documents at once, or to print to several different printers.

```js
var config = qz.configs.create();
var data = [{ type: 'image', data: null, }];

data.data = 'assets/img/my_first_image.png';       ////// First document
config.setPrinter('First Printer');                ////// First printer
qz.print(config, data)

.then(function() {
   data.data = 'assets/img/my_second_image.png';   ////// Second document
   config.setPrinter('Second Printer');            ////// Second printer
   return qz.print(config, data);
})

.then(function() {
   data.data = 'assets/img/my_third_image.png';    ////// Third document
   config.setPrinter('Third Printer');             ////// Third printer
   return qz.print(config, data);
})

.catch(function(e) {
   console.error(e);                // Exceptions throw all the way up the stack
});
   ```

### Promise Loop

Using a Promise Loop, we can defer items (printers, data)

* So that different chunks of data can be sent to different printers
* So that an arbitrary (non-fixed) amount of data can be sent to a printer

```js
function promiseLoop() {
    var data = [
        { "type": "pdf", "data": "assets/sample_pdf1.pdf" },
        { "type": "pdf", "data": "assets/sample_pdf2.pdf" },
        { "type": "pdf", "data": "assets/sample_pdf3.pdf" },
        { "type": "pdf", "data": "assets/sample_pdf3.pdf" },
        { "type": "pdf", "data": "assets/sample_pdf5.pdf" }
    ];
    var configs = [
        { "printer": "HP LaserJet M605" },
        { "printer": "HP LaserJet M605" },
        { "printer": "HP LaserJet M605" },
        { "printer": "HP LaserJet M605" },
        { "printer": "HP LaserJet M605" }
    ];
    var chain = [];

    for(var i = 0; i < data.length; i++) {
        (function(i_) {
            //setup this chain link
            var link = function() {
                return qz.printers.find(configs[i_].printer).then(function(found) {
                    return qz.print(qz.configs.create(found), [data[i_]]);
                });
            };

            chain.push(link);
        })(i);
        //closure ensures this promise's concept of `i` doesn't change
    }

    //can be .connect or `Promise.resolve()`, etc
    var firstLink = new RSVP.Promise(function(r, e) { r(); });

    var lastLink = null;
    chain.reduce(function(sequence, link) {
        lastLink = sequence.then(link);
        return lastLink;
    }, firstLink);

    //this will be the very last link in the chain
    lastLink.catch(function(err) {
        console.error(err);
    });
}
```