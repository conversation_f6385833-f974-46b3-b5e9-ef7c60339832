### Compatibility 

* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :white_check_mark: 2.0 | :white_check_mark: 1.9 | [...](Roadmap)

### Background

1. Renewals only.  For new orders, see [generate certificate](generate-certificate) steps instead.
1. Any remaining days on your previous certificate will roll into the renewed certificate.
1. Renewal discounts are applied automatically.  Contact [support](/support/) with any questions.
1. Multi-year discounts are applied automatically.  Contact [support](/support/) with any questions.

### Steps

#### Renew Support
###### Renew License

1. Navigate to https://buy.qz.io/myaccount.asp (alternatively, https://buy.qz.io and click "My Account" at the top of the screen)

1. Once signed in click "Buy Now" at the top of the screen.

1. Select **ADD TO CART** on the appropriate product (**Premium Support** -- or -- **Company Branded + Premium Support**)

   > <img src="https://user-images.githubusercontent.com/6345473/********-44b80280-1f65-11eb-9d43-181e513394f6.png" width="700px"/>

1. Enter in the desired years you wish to purchase and select **PROCEED TO CHECKOUT**.

   > <img src="https://user-images.githubusercontent.com/6345473/********-995b7d80-1f65-11eb-9d9c-1450f587b698.png" width="700px"/><br>
   > **Note:**  Price should be discounted for renewals, [reach out to support](/support/) if a discount does not display.

1. **IMPORTANT**: Towards to bottom of the page, there will be a section to add in your current product key.  After putting in your product key, along with all of the other required information, select **CHECKOUT**

   > <img src="https://user-images.githubusercontent.com/6345473/********-2e5e7680-1f66-11eb-9340-37e18586c4f9.png" width="700px"/>

1. You will be emailed and assigned a new product key.  Any remaining days on your previous certificate will roll into the renewal.

#### Renew Certificate

1. Log in here https://qz.io/login/ using your email and newly assigned product key

   <img src="https://user-images.githubusercontent.com/6345473/98278673-ee4bc380-1f66-11eb-87c4-6e8e4225480a.png" width="500px"/>

1. Select QZ Tray
 
   <img width="361" alt="image" src="https://user-images.githubusercontent.com/6345473/*********-37c3ca6f-38be-43ce-8bde-3e447d8a03b2.png">

1.  If you do not need to change any of the information on the certificate from the previous year (e.g. Company Name), it is recommended to click the "Renew certificate" button at the top of the page. This is the recommended option, only `digital-certificate.txt` needs to be replaced on the server.

    <img width="1120" alt="image" src="https://user-images.githubusercontent.com/6345473/*********-33e3b765-3233-4ab0-a945-c36f9512403b.png">

1. Alternately, if you need to change the information, click "Show other options" and take the default settings.

    <img width="1119" alt="image" src="https://user-images.githubusercontent.com/6345473/*********-b85219ba-4eef-4750-82a7-c1bc21b00b07.png">


### Replacing Certificate

* Use the `digital-certificate.txt` on your website in place of the old copy(s)

    * The certificate is generally located in a public directory accessible by JavaScript `$.ajax()` or `fetch()`.
    * Find and replace `digital-certificate.txt` on your web server.
       * If the contents of this file were instead pasted directly into code, search project for `-----BEGIN CERTIFICATE-----`.
    * Since QZ Tray 2.0.8, [simply replacing `digital-certificate.txt` is all that is needed](https://github.com/qzind/tray/commit/b048ff49e3bfe655688dd064bf7e0fbff0cdc73c).

    #### Replacing Certificate (Deprecated)
    Websites that were integrated prior to QZ Tray 2.0.8 may need to manually clear the cache. 

    <details><summary>Click <ins>here</ins> to expand steps</summary>

   * **⚠️ WARNING:** The `?modify_url` technique is available for historical purposes and discouraged.  Instead, please use a [`cache` flag](https://github.com/qzind/tray/commit/b048ff49e3bfe655688dd064bf7e0fbff0cdc73c) for `$.ajax()`, `fetch()`, etc.
   * You can force a reload of the cache by modifying the URL. e.g.
      ```diff
      - http://foo/bar/digital-certificate.txt
      + http://foo/bar/digital-certificate.txt?modify_url
      ```

   * If the above URL is referenced from a JavaScript file, the script file may need to be force refreshed as well, e.g:

      ```diff
      - <script src="foo/bar/myscript.js"></script>
      + <script src="foo/bar/myscript.js?modify_url"></script>
      ```

    </details>

### Verifying Certificate

To ensure that the certificate has been replaced properly, you must visit a webpage which uses QZ Tray.
1. Visit a webpage which uses QZ Tray **AND uses the recently deployed certificate.**
2. Ensure QZ Tray is functioning properly by **listing printers** or **doing a test print**.<br>(You may also use serial, USB or file communication as a test.)
3. **:warning: IMPORTANT:** From the QZ Tray menu:
   1. Click **Advanced** > **Site Manager**
   2. Ensure the **"Valid To"** date is correctly updated<br><img width="460" alt="Screenshot 2023-07-27 at 12 57 52 PM" src="https://github.com/qzind/tray/assets/6345473/c03ba844-f402-4789-bddd-6df10976bdf1">
   3. If the certificate was replaced properly there will be **no warnings** when printing.  If you receive a message **Expired certificate** or **Invalid signature**, please reach out to [support](/support/) immediately for assistance.


### Replacing Private Key
Most renewals will re-use the old `private-key.pem` or `private-key.pfx`.  These steps are provided ONLY in the event that the `private-key.pem` or `private-key.pfx` was replaced as part of the renewal process.  Do NOT perform these steps unless you explicitly generated a new private key above.
<details><summary>Click <ins>here</ins> to expand steps</summary>

   * If a new `private-key.pem` or `private-key.pfx` was generated above replace this as well.  This varies per-platform.  Please contact a developer <NAME_EMAIL> on copy of communications.
   * The private key is generally located in a non-public directory, used by a [signing mechanism](https://qz.io/wiki/signing-messages).  This is generally referenced in a file named `sign-message.php` or [similar](https://github.com/qzind/tray/tree/master/assets/signing).
   * If the contents of the private key were instead pasted directly into code, search project for `-----BEGIN PRIVATE KEY-----`.
    </details>

Questions, comments or concerns, please contact [support](/support/)