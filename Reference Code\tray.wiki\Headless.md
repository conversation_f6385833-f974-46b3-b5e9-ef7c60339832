### Compatibility

* :white_check_mark: 2.2 | :white_check_mark: 2.1 | :no_entry: 2.0 | :no_entry: 1.9 | [...](Roadmap)

### Objective

* Run QZ Tray in a headless environment
* See also [windows-service](windows-service)

### Prerequisites

* A Certificate (`digital-certificate.txt` if generated from our portal)

   > **Note:** Step 2 of the [signing messages tutorial](https://qz.io/wiki/2.0-signing-messages#loading-the-trusted-certificate) needs to be completed in order to establish a websocket connection

### Steps

1. Whitelist the certificate

   The first step is to whitelist the Certificate your signatures will be authenticating against. You can launch the software prior to this, but you will not be able to make a websocket connection, as there is no GUI for the warning dialogues to display in, causing an immediate disconnect.


   * Open a terminal or `cmd` prompt and execute the appropriate command for your OS:
     > **Note:** the `--whitelist` flag can be substituted with `--allow` or `-a`

     |OS |Command |
     |---|--------|
     |Windows| `"%PROGRAMFILES%\QZ Tray\qz-tray-console.exe" --whitelist "path\to\digital-certificate.txt"`<br>... and optionally `copy "%appdata%\qz\allowed.dat" "%programdata%\qz\allowed.dat"` see also [`#749`](https://github.com/qzind/tray/issues/749). |
     |Linux| `/opt/qz-tray/qz-tray --whitelist "path/to/digital-certificate.txt"`|
     |macOS| `"/Applications/QZ Tray.app/Contents/MacOS/QZ Tray" --whitelist "path/to/digital-certificate.txt"`|

   * Verify the entry `Successfully added YOUR COMPANY to allowed list` in the log displayed in the command prompt

   * An `allowed.dat` file is created with the finger print of the Intermediate Certificate (`digital-certificate.txt`).

     #### User Scope
     |OS |Location |
     |---|-----------------------|
     |Windows |`%APPDATA%\qz\allowed.dat` |
     |Linux |`~/.qz/allowed.dat`|
     |macOS|`"~/Library/Application Support/qz/allowed.dat"` |

     #### System Wide
     |OS |Location |
     |---|-----------------------|
     |Windows |`%PROGRAMDATA%\qz\allowed.dat` |
     |Linux |`/srv/qz/allowed.dat`|
     |macOS|`"/Library/Application Support/qz/allowed.dat"` |

1. Launch QZ Tray in headless mode

   * Run the software from the command line using the `--headless` or `-h` flag:

     |OS |Command |
     |---|--------|
     |Windows|`"%PROGRAMFILES%\QZ Tray\qz-tray-console.exe" --headless` |
     |Linux|`/opt/qz-tray/qz-tray --headless` |
     |macOS|`"/Applications/QZ Tray.app/Contents/MacOS/QZ Tray" --headless` |